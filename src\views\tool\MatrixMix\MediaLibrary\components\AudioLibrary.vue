<template>
  <div class="audio-library">
    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" @click="handleUpload">
          <el-icon><Plus /></el-icon>
          上传音频
        </el-button>
        <el-button type="danger" @click="handleDelete" :disabled="selectedItems.length === 0">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="right-actions">
        <el-select v-model="businessType" placeholder="业务类型" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="source" placeholder="来源" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="status" placeholder="资源状态" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px; margin-right: 10px;"
        />
        <el-select v-model="sortOrder" placeholder="排序方式" style="width: 100px; margin-right: 10px;">
          <el-option label="升序" value="asc" />
          <el-option label="降序" value="desc" />
        </el-select>
        <el-button type="primary" @click="handleFilter">筛选</el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 右侧音频列表 -->
      <div class="audio-panel">
        <div v-if="loading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
        
        <div v-else-if="props.mediaList?.length === 0" class="empty-state">
          <el-empty description="暂无数据">
            <el-button type="primary" @click="handleUpload">上传音频</el-button>
          </el-empty>
        </div>

        <div v-else class="audio-table">
          <el-table :data="props.mediaList || []" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="音频名称" prop="MediaBasicInfo.Title" min-width="200">
              <template #default="{ row }">
                <div class="audio-title">
                  <el-icon class="audio-icon"><Microphone /></el-icon>
                  <span>{{ row.MediaBasicInfo.Title }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="时长" width="100">
              <template #default="{ row }">
                {{ formatDuration(parseFloat(row.FileInfoList?.[0]?.FileBasicInfo?.Duration || '0')) }}
              </template>
            </el-table-column>
            <el-table-column label="文件大小" width="120">
              <template #default="{ row }">
                {{ formatFileSize(parseInt(row.FileInfoList?.[0]?.FileBasicInfo?.FileSize || '0')) }}
              </template>
            </el-table-column>
            <el-table-column label="创建日期" width="150">
              <template #default="{ row }">
                {{ formatDate(row.MediaBasicInfo.CreateTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250">
              <template #default="{ row }">
                <el-button size="small" @click="handlePlay(row)">
                  <el-icon><VideoPlay /></el-icon>
                  播放
                </el-button>
                <el-button size="small" type="primary" @click="handleDownload(row)">下载</el-button>
                <el-button size="small" type="danger" @click="handleDeleteSingle(row.MediaId)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 音频播放器 -->
    <div v-if="currentAudio" class="audio-player">
      <div class="player-info">
        <el-icon><Microphone /></el-icon>
        <span>{{ currentAudio.title }}</span>
      </div>
      <div class="player-controls">
        <el-button :icon="isPlaying ? 'VideoPause' : 'VideoPlay'" circle @click="togglePlay" />
        <div class="progress-bar">
          <el-slider v-model="progress" :max="100" @change="handleProgressChange" />
        </div>
        <span class="time">{{ currentTime }} / {{ totalTime }}</span>
      </div>
      <div class="player-actions">
        <el-button size="small" @click="closePlayer">关闭</el-button>
      </div>
    </div>

    <!-- 上传组件 -->
    <MediaUploader
      v-model:visible="uploadDialogVisible"
      media-type="audio"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Delete,
  Refresh,
  Loading,
  VideoPlay,
  Microphone
} from '@element-plus/icons-vue'
import type { MediaListQueryParams, MediaInfo } from '../../types/media';
import { formatFileSize, formatDuration } from '../../utils/commonUtils'
import { BusinessTypeMap, StatusMap, SourceMap, mapToOptions } from '../../types/media'
import MediaUploader from './add/MediaUploader.vue'

// 定义组件的事件
const emit = defineEmits<{
  upload: [uploadParams: { file: File; MediaMetaData?: any }],
  refreshMediaList: [page?: number],
  sizeChange: [size: number],
  pageChange: [current: number]
}>()

// 响应式数据
const loading = ref(false)
const selectedItems = ref<string[]>([])
const uploadDialogVisible = ref(false)
const currentAudio = ref<any>(null)
const isPlaying = ref(false)
const progress = ref(0)
const currentTime = ref('00:00')
const totalTime = ref('00:00')
const uploadFiles = ref<File[]>([]) // 添加上传文件列表

// 分页事件 emit 给父组件
const handleSizeChange = (size: number) => {
  emit('sizeChange', size)
}

const handleCurrentChange = (current: number) => {
  emit('pageChange', current)
}

// 筛选相关变量和选项
const businessType = ref('')
const source = ref('')
const status = ref('')
const dateRange = ref<[Date, Date] | null>(null)
const sortOrder = ref('desc')

const businessTypeOptions = mapToOptions(BusinessTypeMap)
const statusOptions = mapToOptions(StatusMap)
const sourceOptions = mapToOptions(SourceMap)

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const uploadAction = computed(() => {
  return '#' // 不使用实际的上传地址，改为使用自定义上传
})

// 方法
const handleUpload = () => {
  uploadDialogVisible.value = true
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除选中的音频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    selectedItems.value = []
    emit('refreshMediaList') // 刷新列表
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleRefresh = () => {
  emit('refreshMediaList') // 刷新列表
}

const handleSelectionChange = (selection: MediaInfo[]) => {
  selectedItems.value = selection.map(item => item.MediaId)
}

const handlePlay = (audio: any) => {
  currentAudio.value = audio
  isPlaying.value = true
  ElMessage.success(`开始播放: ${audio.MediaBasicInfo.Title}`)
}

const handleDownload = (audio: any) => {
  ElMessage.info(`下载音频: ${audio.MediaBasicInfo.Title}`)
}

const handleDeleteSingle = async (audioId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个音频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    emit('refreshMediaList') // 刷新列表
  } catch {
    ElMessage.info('已取消删除')
  }
}

const togglePlay = () => {
  isPlaying.value = !isPlaying.value
}

const handleProgressChange = (value: number) => {
  progress.value = value
}

const closePlayer = () => {
  currentAudio.value = null
  isPlaying.value = false
  progress.value = 0
}

// 上传成功处理
const handleUploadSuccess = (result: any) => {
  ElMessage.success('音频上传成功！')
  uploadDialogVisible.value = false
  emit('refreshMediaList')
}

// 上传失败处理
const handleUploadError = (error: any) => {
  ElMessage.error('音频上传失败: ' + (error.message || '未知错误'))
}

// 监听上传成功事件
const handleMediaUploaded = (event: Event) => {
  const customEvent = event as CustomEvent
  const { mediaId, category, fileName, fileInfo, mediaInfo } = customEvent.detail
  if (category === 'audio') {
    ElMessage.success(`音频上传成功！文件: ${fileName}，媒资ID: ${mediaId}`)
    // 刷新音频列表
    emit('refreshMediaList') // 通知父组件刷新
  }
}

// props 增加分页相关
const props = defineProps<{
  mediaResponse?: any,
  mediaList?: MediaInfo[],
  currentPage?: number,
  pageSize?: number,
  total?: number
}>()

// 监听props变化，更新列表
watch(
  () => props.mediaResponse,
  (val) => {
    if (val && Array.isArray(val.MediaInfos)) {
      // 父组件已处理分页，mediaList/total 由 props 传递
    } else {
      // 父组件已处理分页，mediaList/total 由 props 传递
    }
  },
  { immediate: true }
)

// 筛选事件 - 仅触发父组件刷新，父组件需要获取当前筛选值
const handleFilter = () => {
  // Reset to first page on filter
  // currentPage.value = 1; // This is now handled by parent
  emit('refreshMediaList', 1);
}

onMounted(() => {
  // 初始化数据
  emit('refreshMediaList') // 通知父组件加载数据
  // 监听上传成功事件
  window.addEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})
</script>

<style lang="scss" scoped>
.audio-library {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .left-actions {
    display: flex;
    gap: 10px;
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.content-wrapper {
  display: flex;
  flex: 1;
  gap: 20px;
  min-height: 0;
}

.category-panel {
  width: 280px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;

  .category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
  }

  .search-box {
    margin-bottom: 15px;
  }

  .category-list {
    .category-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      cursor: pointer;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      &.active {
        background-color: #409eff;
        color: white;
      }

      .count {
        font-size: 12px;
        opacity: 0.7;
      }
    }
  }
}

.audio-panel {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 10px;
    color: #666;

    .el-icon {
      font-size: 32px;
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
  }

  .audio-table {
    flex: 1;

    .audio-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .audio-icon {
        color: #409eff;
      }
    }
  }
}

.audio-player {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  min-width: 500px;
  z-index: 1000;

  .player-info {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;

    .el-icon {
      color: #409eff;
    }
  }

  .player-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;

    .progress-bar {
      flex: 1;
      margin: 0 10px;
    }

    .time {
      font-size: 12px;
      color: #666;
      min-width: 80px;
    }
  }

  .player-actions {
    .el-button {
      margin: 0;
    }
  }
}

.upload-demo {
  .el-icon--upload {
    font-size: 48px;
    color: #409eff;
  }
}
</style>
