// ICE SDK 类型定义 - 本地版本
// 基于阿里云 ICE SDK 官方文档定义

export interface IceEditingProject {
  projectId: string;
  title: string;
  description?: string;
  timeline?: IceTimeline;
  status?: string;
  createTime?: string;
  modifyTime?: string;
  coverURL?: string;
  duration?: number;
}

export interface IceTimeline {
  timelineId?: string;
  videoTracks?: IceTimelineTrack[];
  audioTracks?: IceTimelineTrack[];
  duration?: number;
}

export interface IceTimelineTrack {
  trackId: string;
  type: 'Video' | 'Audio' | 'Text';
  clips?: IceTimelineClip[];
  height?: number;
  width?: number;
}

export interface IceTimelineClip {
  clipId: string;
  materialId?: string;
  type: 'Video' | 'Audio' | 'Image' | 'Text';
  in: number;
  out: number;
  timelineIn: number;
  timelineOut: number;
  speed?: number;
  volume?: number;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  text?: string;
  fontName?: string;
  fontSize?: number;
  fontColor?: string;
}

export interface IceProjectMaterial {
  materialId: string;
  title: string;
  type: 'Video' | 'Audio' | 'Image';
  fileURL: string;
  coverURL?: string;
  duration?: number;
  width?: number;
  height?: number;
  size?: number;
  createTime?: string;
}

export interface IceTemplate {
  templateId: string;
  title: string;
  description?: string;
  coverURL?: string;
  type?: string;
  tags?: string[];
  config?: any;
}

export interface MediaItem {
  mediaId: string;
  title: string;
  type: 'Video' | 'Audio' | 'Image';
  fileURL: string;
  coverURL?: string;
  duration?: number;
  width?: number;
  height?: number;
  size?: number;
  createTime?: string;
  tags?: string[];
}

export interface TimelineConfig {
  width: number;
  height: number;
  fps: number;
  duration: number;
}

export interface MediaClip {
  id: string;
  type: 'video' | 'audio' | 'image' | 'text';
  url: string;
  startTime: number;
  endTime: number;
  duration: number;
  trackId?: string;
}

export interface ICEConfig {
  accessKeyId: string;
  accessKeySecret: string;
  endpoint: string;
  regionId?: string;
}

// API 响应类型
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: string;
}

// 项目创建参数
export interface CreateProjectParams {
  title: string;
  description?: string;
  timeline?: IceTimeline;
}

// 项目更新参数
export interface UpdateProjectParams {
  projectId: string;
  title?: string;
  description?: string;
  timeline?: IceTimeline;
}

// 媒体搜索参数
export interface SearchMediaParams {
  keyword?: string;
  type?: 'Video' | 'Audio' | 'Image';
  pageNo?: number;
  pageSize?: number;
}

// 导出配置
export interface ExportConfig {
  format: 'mp4' | 'mov' | 'avi';
  resolution: '720p' | '1080p' | '4k';
  quality: 'low' | 'medium' | 'high';
  fps?: number;
}
