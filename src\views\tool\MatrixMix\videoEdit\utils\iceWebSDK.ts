/**
 * 阿里云ICE WebSDK封装 - 统一版本
 * 同时支持云剪辑项目和模板工厂模板编辑
 */
import { iceNPMManager, EditorMode } from './iceNPMManager';
import { ElMessage } from 'element-plus';
import {
  CustomFontItem,
  VoiceGroup,
  AvatarConfig,
  VideoTranslation,
  Timeline,
  TimelineMaterial,
  ASRResult,
  VoiceConfig,
  StickerCategory,
  StickerResponse,
} from './iceTypes';

export interface UnifiedWebSDKConfig {
  container: HTMLElement;
  id: string; // 项目ID或模板ID
  mode: EditorMode; // 'project' | 'template'
}

export interface AliyunVideoEditor {
    init: (config: any) => void;
    destroy: (keepState?: boolean) => boolean;
    version: string;
    setCurrentTime: (currentTime: number) => void;
    getCurrentTime: () => number;
    getDuration: () => number;
}

/**
 * ICE WebSDK管理器 - 统一版本
 */
class UnifiedICEWebSDK {
  private editorInstance: AliyunVideoEditor | null = null;
  private currentId: string | null = null;
  private currentMode: EditorMode | null = null;
  private isInitializing = false;
  private callbackCache = new Map<string, any>();
  private activeRequests = new Map<string, Promise<any>>();
  private materialSelectorCallback: ((mediaType: 'video' | 'audio' | 'image') => Promise<any[]>) | null = null;

  /**
   * 设置素材选择器回调
   */
  setMaterialSelectorCallback(callback: (mediaType: 'video' | 'audio' | 'image') => Promise<any[]>) {
    this.materialSelectorCallback = callback;
  }

  /**
   * 初始化WebSDK - 统一处理云剪辑和模板工厂
   */
  async init(config: UnifiedWebSDKConfig): Promise<boolean> {
    if (this.isInitializing) {
      return false;
    }

    if (this.editorInstance) {
      this.destroy();
    }

    this.isInitializing = true;

    try {
      // 容器安全检查
      if (!config.container || !config.container.isConnected) {
        throw new Error('容器元素无效或未连接到DOM');
      }

      // 标记容器为非Vue管理
      (config.container as any).__VUE_SKIP__ = true;

      // 清空容器，防止冲突
      config.container.innerHTML = '';

      // 初始化NPM管理器
      await iceNPMManager.initialize();

      // 检查WebSDK是否加载
      if (!(window as any).AliyunVideoEditor) {
        throw new Error('AliyunVideoEditor WebSDK未加载，请检查脚本引入');
      }

      this.editorInstance = (window as any).AliyunVideoEditor;
      this.currentId = config.id;
      this.currentMode = config.mode;

      // 创建配置对象
      const websdkConfig = this.createWebSDKConfig(config);

      // 在安全的异步上下文中初始化WebSDK
      await new Promise<void>((resolve, reject) => {
        try {
          const editor = this.editorInstance;
          if (!editor) {
            reject(new Error('WebSDK实例获取失败'));
            return;
          }

          requestAnimationFrame(() => {
            try {
              editor.init(websdkConfig);

              setTimeout(() => {
                this.optimizeWebSDKContainer(config.container);
                resolve();
              }, 100);

            } catch (error) {
              reject(error);
            }
          });

        } catch (error) {
          reject(error);
        }
      });

      return true;

    } catch (error: any) {
      console.error('WebSDK初始化失败:', error);
      return false;
    } finally {
      this.isInitializing = false;
    }
  }

  /**
   * 创建WebSDK配置
   */
  private createWebSDKConfig(config: UnifiedWebSDKConfig): any {
    const baseConfig = {
      container: config.container,
      locale: 'zh-CN',
      mode: config.mode,
      
      // 基础配置
    //   defaultAspectRatio: { width: 16, height: 9 } as PlayerAspectRatio,
      defaultSubtitleText: '阿里云剪辑',
      useDynamicSrc: true,
      dynamicSrcQps: 10,
      
      // 功能开关配置
      disableAutoJobModal: false,
      disableGreenMatting: false,
      disableRealMatting: false,
      disableDenoise: false,
      audioWaveRenderDisabled: false,
      disableAutoAspectRatio: false,
      hasTranscodedAudio: false,

    //   // License配置
    //   licenseConfig: {
    //     licenseKey: process.env.VUE_APP_ICE_LICENSE_KEY || ''
    //   } as LicenseConfig,

      // 动态获取媒资播放地址
      getDynamicSrc: this.createDynamicSrcGetter(),

      // 获取Timeline中的素材媒资信息
      getTimelineMaterials: this.createTimelineMaterialsGetter(),

      // AI功能配置
      submitASRJob: this.createASRJobSubmitter(),
      submitAudioProduceJob: this.createAudioProduceJobSubmitter(),

      // ASR和TTS配置
      asrConfig: {
        submitASRJob: this.createASRJobSubmitter()
      },

      ttsConfig: {
        submitAudioProduceJob: this.createAudioProduceJobSubmitter()
      },

      // 公共素材库配置
      publicMaterials: {
        getLists: async () => []
      },

      // 字幕配置
      subtitleConfig: {},

      // 贴纸相关配置
      getStickerCategories: this.createStickerCategoriesGetter(),
      getStickers: this.createStickersGetter(),

      // 自定义配置
      customTexts: {
        importButton: '导入素材',
        updateButton: config.mode === 'template' ? '保存模板' : '保存项目',
        produceButton: '生成视频',
        backButton: '返回',
        logoUrl: '/logo.png'
      },

      customFontList: [
        '微软雅黑',
        '宋体',
        '黑体',
        {
          key: 'alibaba-font',
          name: '阿里巴巴普惠体',
          url: 'https://example.com/fonts/alibaba.woff2'
        } as CustomFontItem
      ],

      customVoiceGroups: [
        {
          type: 'standard',
          category: '标准语音',
          voiceList: []
        }
      ] as VoiceGroup[],

      // 水印配置
      getPreviewWaterMarks: async () => {
        return [
          {
            url: '/watermark.png',
            width: 0.2,
            height: 0.1,
            x: 0.8,
            y: 0.9,
            opacity: 0.5
          }
        ];
      },

      // 数字人配置
      avatarConfig: {
        enabled: false
      } as AvatarConfig,

      // 视频翻译配置
      videoTranslation: {
        enabled: false,
        languages: ['en', 'ja', 'ko']
      } as VideoTranslation,

      // 导出轨道/视频片段拆分
      exportVideoClipsSplit: this.createVideoClipsSplitExporter(),
      // 导出片段（从标记点导出）
      exportFromMediaMarks: this.createMediaMarksExporter(),
      // 合并导出
      exportVideoClipsMerge: this.createVideoClipsMergeExporter(),

      // 音频优化配置
      getAudioByMediaId: this.createAudioGetter()
    };

    // 根据模式添加特定配置
    if (config.mode === 'template') {
      return {
        ...baseConfig,
        getEditingProjectMaterials: this.createTemplateProjectMaterialsGetter(config.id),
        searchMedia: this.createTemplateSearchMedia(config.id),
        deleteEditingProjectMaterials: this.createTemplateDeleteMaterials(config.id),
        getEditingProject: this.createTemplateGetProject(config.id),
        updateTemplate: this.createTemplateUpdater(config.id),
        // 模板模式不需要这两个回调
        updateEditingProject: undefined,
        produceEditingProjectVideo: undefined
      };
    } else {
      return {
        ...baseConfig,
        getEditingProjectMaterials: this.createProjectMaterialsGetter(config.id),
        searchMedia: this.createProjectSearchMedia(config.id),
        deleteEditingProjectMaterials: this.createProjectDeleteMaterials(config.id),
        getEditingProject: this.createProjectGetProject(config.id),
        updateEditingProject: this.createProjectUpdater(config.id),
        produceEditingProjectVideo: this.createVideoProducer(config.id),
        // 项目模式不需要模板更新回调
        updateTemplate: undefined
      };
    }
  }

  /**
   * 创建动态资源获取器
   */
  private createDynamicSrcGetter() {
    return async (mediaId: string, mediaType: string, mediaOrigin?: 'private' | 'public', inputUrl?: string) => {
      const requestKey = `getDynamicSrc_${mediaId}`;
      return this.executeWithDeduplication(requestKey, async () => {
        try {
          if (mediaOrigin === 'public' && inputUrl) {
            return inputUrl;
          }

          const response = await iceNPMManager.batchGetMediaInfos({ mediaIds: [mediaId] });
          let mediaInfos = [];
          if (response?.data?.MediaInfos) {
            mediaInfos = response.data.MediaInfos;
          } else if ((response as any)?.MediaInfos) {
            mediaInfos = (response as any).MediaInfos;
          }

          if (mediaInfos.length > 0) {
            const mediaInfo = mediaInfos[0];
            const fileInfoList = mediaInfo.FileInfoList || mediaInfo.MediaInfo?.FileInfoList || [];
            const fileBasicInfo = fileInfoList[0]?.FileBasicInfo;
            return fileBasicInfo?.FileUrl || '';
          }
          return '';
        } catch (error) {
          console.error('获取动态媒资URL失败:', error);
          return '';
        }
      });
    };
  }

  /**
   * 创建Timeline素材获取器
   */
  private createTimelineMaterialsGetter() {
    return async (params: TimelineMaterial[]) => {
      try {
        if (!params || params.length === 0) return [];
        
        const mediaIds = params.map(p => p.mediaId);
        const materialsData = await this.getMediaDetailsByIds(mediaIds);
        return this.transMediaList(materialsData);
      } catch (error) {
        console.error('获取Timeline素材失败:', error);
        return [];
      }
    };
  }

  /**
   * 创建模板项目素材获取器
   */
  private createTemplateProjectMaterialsGetter(templateId: string) {
    return this.createCachedCallback('getEditingProjectMaterials', async () => {
      try {
        const template = await iceNPMManager.getTemplate(templateId, 1);
        if (template.RelatedMediaids) {
          const mediaIdsMap = JSON.parse(template.RelatedMediaids);
          const mediaIds = Object.values(mediaIdsMap).reduce((acc: string[], cur: any) => 
            acc.concat(Array.isArray(cur) ? cur : [cur]), []
          );
          if (mediaIds.length > 0) {
            const materialsData = await this.getMediaDetailsByIds(mediaIds);
            return this.transMediaList(materialsData);
          }
        }
        return [];
      } catch (error) {
        console.error('获取模板素材失败:', error);
        return [];
      }
    });
  }

  /**
   * 创建云剪辑项目素材获取器
   */
  private createProjectMaterialsGetter(projectId: string) {
    return this.createCachedCallback('getEditingProjectMaterials', async () => {
      try {
        const mediaInfos = await iceNPMManager.getEditingProjectMaterials(projectId);
        return this.transMediaList(mediaInfos);
      } catch (error) {
        console.error('获取项目素材失败:', error);
        return [];
      }
    });
  }

  /**
   * 创建模板搜索媒体函数
   */
  private createTemplateSearchMedia(templateId: string) {
    return this.createCachedCallback('searchMedia', async (mediaType: 'video' | 'audio' | 'image') => {
      try {
        // 使用回调函数而不是全局方法
        if (this.materialSelectorCallback) {
          const selectedMaterials = await this.materialSelectorCallback(mediaType);
          
          if (selectedMaterials && selectedMaterials.length > 0) {
            // 更新模板的RelatedMediaids
            const template = await iceNPMManager.getTemplate(templateId, 1);
            let mediaIdsMap: Record<string, string[]> = {};
            
            if (template.RelatedMediaids) {
              mediaIdsMap = JSON.parse(template.RelatedMediaids);
            }
            
            if (!mediaIdsMap[mediaType]) {
              mediaIdsMap[mediaType] = [];
            }
            
            // 添加新选择的素材ID
            const newMediaIds = selectedMaterials.map((material: any) => material.mediaId);
            mediaIdsMap[mediaType] = [...new Set([...mediaIdsMap[mediaType], ...newMediaIds])];
            
            await iceNPMManager.updateTemplate(templateId, {
              relatedMediaids: JSON.stringify(mediaIdsMap)
            });
            
            return selectedMaterials;
          }
        } else {
          ElMessage.error('素材选择器未初始化');
        }
        
        return [];
      } catch (error) {
        console.error('搜索模板素材失败:', error);
        return [];
      }
    });
  }

  /**
   * 创建云剪辑搜索媒体函数
   */
  private createProjectSearchMedia(projectId: string) {
    return this.createCachedCallback('searchMedia', async (mediaType: 'video' | 'audio' | 'image') => {
      try {
        // 使用回调函数而不是全局方法
        if (this.materialSelectorCallback) {
          const selectedMaterials = await this.materialSelectorCallback(mediaType);
          
          if (selectedMaterials && selectedMaterials.length > 0) {
            // 添加素材到项目
            const mediaIds = selectedMaterials.map((material: any) => material.mediaId);
            await iceNPMManager.addEditingProjectMaterials(projectId, mediaIds);
            
            return selectedMaterials;
          }
        } else {
          ElMessage.error('素材选择器未初始化');
        }
        
        return [];
      } catch (error) {
        console.error('搜索项目素材失败:', error);
        return [];
      }
    });
  }

  /**
   * 创建模板删除素材函数
   */
  private createTemplateDeleteMaterials(templateId: string) {
    return async (mediaId: string, mediaType: string) => {
      try {
        const template = await iceNPMManager.getTemplate(templateId, 1);
        
        if (template.RelatedMediaids) {
          const mediaIdsMap: Record<string, string[]> = JSON.parse(template.RelatedMediaids);
          
          if (mediaIdsMap[mediaType] && mediaIdsMap[mediaType].includes(mediaId)) {
            mediaIdsMap[mediaType] = mediaIdsMap[mediaType].filter((id: string) => id !== mediaId);
            
            await iceNPMManager.updateTemplate(templateId, {
              relatedMediaids: JSON.stringify(mediaIdsMap)
            });
            
            return;
          }
        }
        
        throw new Error('素材不存在或删除失败');
      } catch (error) {
        console.error('删除模板素材失败:', error);
        throw error;
      }
    };
  }

  /**
   * 创建云剪辑删除素材函数
   */
  private createProjectDeleteMaterials(projectId: string) {
    return async (mediaId: string, mediaType: string) => {
      const requestKey = `deleteEditingProjectMaterials_${projectId}_${mediaId}`;
      return this.executeWithDeduplication(requestKey, async () => {
        try {
          const success = await iceNPMManager.deleteEditingProjectMaterials(
            projectId, 
            mediaType, 
            mediaId
          );
          if (!success) {
            throw new Error('删除项目素材失败');
          }
        } catch (error) {
          console.error('删除项目素材失败:', error);
          throw error;
        }
      });
    };
  }

  /**
   * 创建模板获取项目函数
   */
  private createTemplateGetProject(templateId: string) {
    return async () => {
      try {
        const template = await iceNPMManager.getTemplate(templateId);
        const timeline = template.Config ? JSON.parse(template.Config) : undefined;
        
        return {
          timeline,
          projectId: templateId
        };
      } catch (error) {
        console.error('获取模板失败:', error);
        return { timeline: undefined };
      }
    };
  }

  /**
   * 获取云剪辑工程函数
   */
  private createProjectGetProject(projectId: string) {
    return () => {
      const requestKey = `getEditingProject_${projectId}`;
      return this.executeWithDeduplication(requestKey, async () => {
        try {
          const result = await iceNPMManager.initializeFromProject(projectId);
          if (result && result.data) {
            const projectData = result.data;
            return {
              projectId: projectId,
              timeline: projectData.Timeline || undefined,
              modifiedTime: projectData.ModifiedTime || new Date().toISOString(),
              title: projectData.Title || '未命名项目'
            };
          }
          
          return {
            projectId: projectId,
            timeline: undefined,
            modifiedTime: new Date().toISOString(),
            title: '未命名项目'
          };
        } catch (error) {
          console.error('获取项目失败:', error);
          return {
            projectId: projectId,
            timeline: undefined,
            modifiedTime: new Date().toISOString(),
            title: '未命名项目'
          };
        }
      });
    };
  }

  /**
   * 创建模板保存函数
   */
  private createTemplateUpdater(templateId: string) {
    return async (data: {coverUrl: string; duration: number; timeline: Timeline; isAuto: boolean}) => {
      try {
        await iceNPMManager.updateTemplate(templateId, {
          coverUrl: data.coverUrl,
          duration: data.duration,
          timeline: data.timeline,
          isAuto: data.isAuto
        });
        
        if (!data.isAuto) {
          ElMessage.success({
            message: '模板保存成功！',
            duration: 3000,
            showClose: true
          });
        }
        
        return { projectId: templateId };
      } catch (error) {
        if (!data.isAuto) {
          ElMessage.error({
            message: `模板保存失败: ${error instanceof Error ? error.message : '未知错误'}`,
            duration: 5000,
            showClose: true
          });
        }
        throw error;
      }
    };
  }

  /**
   * 创建云剪辑更新函数
   */
  private createProjectUpdater(projectId: string) {
    return (data: {coverUrl: string; duration: number; timeline: any; isAuto: boolean}) => {
      const requestKey = `updateEditingProject_${projectId}_${Date.now()}`;
      return this.executeWithDeduplication(requestKey, async () => {
        try {
          const success = await iceNPMManager.updateProject({
            projectId: projectId,
            title: undefined,
            timeline: data.timeline
          });

          if (success) {
            if (!data.isAuto) {
              ElMessage.success('项目保存成功');
            }
            return { projectId };
          } else {
            throw new Error('更新项目失败');
          }
        } catch (error) {
          console.error('更新编辑工程失败:', error);
          throw error;
        }
      });
    };
  }

  /**
   * 创建视频生成函数
   */
  private createVideoProducer(projectId: string) {
    return async (data: any) => {
      try {
        // 显示视频生成对话框（需要自行实现）
        ElMessage.info('请实现视频生成对话框');
        return Promise.resolve();
      } catch (error) {
        console.error('生成视频失败:', error);
        return Promise.reject(error);
      }
    };
  }

  /**
   * 创建ASR任务提交器
   */
  private createASRJobSubmitter() {
    return async (mediaId: string, startTime: string, duration: string): Promise<ASRResult[]> => {
      try {
        const response = await iceNPMManager.submitASRJob(mediaId, startTime, duration);
        return response || [];
      } catch (error) {
        console.error('智能字幕识别失败:', error);
        return [];
      }
    };
  }

  /**
   * 创建音频生成任务提交器
   */
  private createAudioProduceJobSubmitter() {
    return async (text: string, voice: string, voiceConfig?: VoiceConfig) => {
      try {
        const response = await iceNPMManager.submitAudioProduceJob(text, voice, voiceConfig);
        return response;
      } catch (error) {
        console.error('文字转语音失败:', error);
        throw error;
      }
    };
  }

  /**
   * 创建贴纸分类获取器
   */
  private createStickerCategoriesGetter() {
    return async (): Promise<StickerCategory[]> => {
      try {
        const mediaTagList = await iceNPMManager.listAllPublicMediaTags(
          'sticker',
          this.editorInstance?.version || ''
        );
        
        return mediaTagList.map((item: any) => ({
          id: item.MediaTagId,
          name: item.MediaTagNameChinese
        }));
      } catch (error) {
        console.error('获取贴纸分类失败:', error);
        return [];
      }
    };
  }

  /**
   * 创建贴纸获取器
   */
  private createStickersGetter() {
    return async (config: {categoryId?: string; page: number; size: number}): Promise<StickerResponse> => {
      try {
        const result = await iceNPMManager.listPublicMediaBasicInfos({
          PageNo: config.page,
          PageSize: config.size,
          IncludeFileBasicInfo: true,
          MediaTagId: config.categoryId
        });
        
        const stickers = (result.MediaInfos || []).map((item: any) => ({
          mediaId: item.MediaId,
          src: item.FileInfoList[0]?.FileBasicInfo?.FileUrl
        }));
        
        return {
          total: result.TotalCount || 0,
          stickers
        };
      } catch (error) {
        console.error('获取贴纸列表失败:', error);
        return { total: 0, stickers: [] };
      }
    };
  }

  /**
   * 创建视频片段拆分导出器
   */
  private createVideoClipsSplitExporter() {
    return async (data: Array<any>) => {
      try {
        // TODO 导出视频片段
        console.log('导出视频片段:', data);
        ElMessage.info('视频片段导出功能待实现');
      } catch (error) {
        throw error;
      }
    };
  }

  /**
   * 创建标记片段导出器
   */
  private createMediaMarksExporter() {
    return async (data: Array<any>) => {
      try {
        console.log('从标记导出:', data);
        ElMessage.info('标记片段导出功能待实现');
      } catch (error) {
        throw error;
      }
    };
  }

  /**
   * 创建视频片段合并导出器
   */
  private createVideoClipsMergeExporter() {
    return async (data: any) => {
      try {
        console.log('合并导出视频:', data);
        ElMessage.info('视频合并导出功能待实现');
      } catch (error) {
        throw error;
      }
    };
  }

  /**
   * 创建音频获取器
   */
  private createAudioGetter() {
    return async (mediaId: string): Promise<string> => {
      try {
        // 获取视频的代理音频地址
        const mediaInfo = await iceNPMManager.getMediaInfo(mediaId);
        // 这里应该返回代理音频地址
        return '';
      } catch (error) {
        return '';
      }
    };
  }

  /**
   * 创建带缓存的回调函数，防止重复调用
   */
  private createCachedCallback<T>(key: string, callback: (...args: any[]) => Promise<T>): (...args: any[]) => Promise<T> {
    const cacheKey = `cached_${key}`;
    const cachedValue = this.callbackCache.get(cacheKey);

    if (cachedValue) {
      return cachedValue;
    }

    const newCallback = async (...args: any[]) => {
      try {
        const result = await callback(...args);
        return result;
      } catch (error) {
        console.error(`执行回调 ${key} 失败:`, error);
        throw error;
      }
    };

    this.callbackCache.set(cacheKey, newCallback);
    return newCallback;
  }

  /**
   * 请求去重执行器
   */
  private executeWithDeduplication<T>(key: string, executor: () => Promise<T>): Promise<T> {
    // 如果相同的请求正在执行，返回相同的Promise
    if (this.activeRequests.has(key)) {
      console.log(`🔄 请求去重: ${key}`);
      return this.activeRequests.get(key) as Promise<T>;
    }

    // 执行新请求
    const promise = executor().finally(() => {
      // 请求完成后清理
      this.activeRequests.delete(key);
    });

    this.activeRequests.set(key, promise);
    return promise;
  }

  /**
   * 优化WebSDK容器，减少事件警告
   */
  private optimizeWebSDKContainer(container: HTMLElement): void {
    try {
      // 查找WebSDK创建的所有子元素
      const allElements = container.querySelectorAll('*');

      allElements.forEach((element: Element) => {
        const htmlElement = element as HTMLElement;

        // 优化事件监听器 - 添加passive事件监听器覆盖
        htmlElement.style.touchAction = 'auto';
        htmlElement.setAttribute('data-passive-events', 'true');

        // 特别处理滚轮事件
        if (htmlElement.addEventListener) {
          // 移除可能存在的非passive wheel事件监听器
          const originalAddEventListener = htmlElement.addEventListener;
          htmlElement.addEventListener = function (type: string, listener: any, options?: any) {
            if (type === 'wheel' || type === 'mousewheel' || type === 'DOMMouseScroll') {
              // 强制设置为passive
              if (typeof options === 'boolean') {
                options = { passive: true, capture: options };
              } else if (typeof options === 'object') {
                options = { ...options, passive: true };
              } else {
                options = { passive: true };
              }
            }
            return originalAddEventListener.call(this, type, listener, options);
          };
        }

        // 如果是画布或视频元素，特别优化
        if (htmlElement.tagName === 'CANVAS' || htmlElement.tagName === 'VIDEO') {
          htmlElement.style.touchAction = 'none';
        }
      });

      // 全局优化滚轮事件
      this.overrideGlobalWheelEvents(container);

    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 全局优化滚轮事件，防止passive警告
   */
  private overrideGlobalWheelEvents(container: HTMLElement): void {
    try {
      // 在WebSDK容器上添加passive wheel事件监听器
      const wheelHandler = (e: Event) => {
        // 不阻止默认行为，让WebSDK正常处理
      };

      container.addEventListener('wheel', wheelHandler, { passive: true });
      (container as any).addEventListener('mousewheel', wheelHandler, { passive: true });
      (container as any).addEventListener('DOMMouseScroll', wheelHandler, { passive: true });

    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 批量获取媒资详细信息
   */
  private async getMediaDetailsByIds(mediaIds: string[]): Promise<any[]> {
    try {
      // 调用批量获取媒资信息API，传入正确的对象参数
      const response = await iceNPMManager.batchGetMediaInfos({ mediaIds });

      // 正确解析响应数据结构
      let mediaInfos = [];
      if (response?.data?.MediaInfos) {
        mediaInfos = response.data.MediaInfos;
      } else if ((response as any)?.MediaInfos) {
        mediaInfos = (response as any).MediaInfos;
      } else if (Array.isArray(response)) {
        mediaInfos = response;
      } else {
        console.warn('⚠️ 无法解析媒资信息响应:', response);
        return [];
      }

      return Array.isArray(mediaInfos) ? mediaInfos.filter(Boolean) : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 将服务端的素材信息转换成 WebSDK 需要的格式
   */
  private transMediaList(data: any[]): any[] {
    if (!data || !Array.isArray(data)) return [];

    return data.map((item, index) => {
      // 适配新的数据结构
      const basicInfo = item.MediaBasicInfo || item.MediaInfo?.MediaBasicInfo || item;
      const fileInfoList = item.FileInfoList || item.MediaInfo?.FileInfoList || [];
      const fileBasicInfo = fileInfoList[0]?.FileBasicInfo;

      const mediaId = basicInfo?.MediaId;
      const mediaType = basicInfo?.MediaType?.toLowerCase() || 'video';

      if (!mediaId) {
        return null;
      }

      // 按照官方文档格式创建结果对象
      const result: any = {
        mediaId,
        mediaType: mediaType
      };

      if (mediaType === 'video') {
        result.video = {
          title: fileBasicInfo?.FileName || basicInfo?.Title || mediaId,
          duration: Number(fileBasicInfo?.Duration) || 0,
          // 源视频的宽高、码率等数据，用于推荐合成数据，不传入或是0时无推荐数据
          width: Number(fileBasicInfo?.Width) || 0,
          height: Number(fileBasicInfo?.Height) || 0,
          bitrate: Number(fileBasicInfo?.Bitrate) || 0,
          coverUrl: basicInfo?.CoverURL || ''
          // 注意：不设置 url 字段，由 getDynamicSrc 动态获取
        };

        // 处理雪碧图信息
        const spriteImages = basicInfo?.SpriteImages;
        if (spriteImages) {
          try {
            const spriteArr = typeof spriteImages === 'string' ? JSON.parse(spriteImages) : spriteImages;
            if (Array.isArray(spriteArr) && spriteArr.length > 0) {
              const sprite = spriteArr[0];
              const config = typeof sprite.Config === 'string' ? JSON.parse(sprite.Config) : sprite.Config;
              result.video.spriteConfig = {
                num: config?.Num,
                lines: config?.SpriteSnapshotConfig?.Lines,
                cols: config?.SpriteSnapshotConfig?.Columns,
                cellWidth: config?.SpriteSnapshotConfig?.CellWidth,
                cellHeight: config?.SpriteSnapshotConfig?.CellHeight
              };
              result.video.sprites = sprite.SnapshotUrlList || [];
            }
          } catch (e) {
            console.log(e);
            // 静默处理雪碧图解析错误
          }
        }
      } else if (mediaType === 'audio') {
        result.audio = {
          title: fileBasicInfo?.FileName || basicInfo?.Title || mediaId,
          duration: Number(fileBasicInfo?.Duration) || 0,
          coverURL: basicInfo?.CoverURL || ''
          // 注意：不设置 url 字段，由 getDynamicSrc 动态获取
        };
      } else if (mediaType === 'image') {
        result.image = {
          title: fileBasicInfo?.FileName || basicInfo?.Title || mediaId,
          coverUrl: basicInfo?.CoverURL || '',
          // 图片的宽高等数据，用于推荐合成数据，不传入或是0时无推荐数据
          width: Number(fileBasicInfo?.Width) || 0,
          height: Number(fileBasicInfo?.Height) || 0
          // 注意：不设置 url 字段，由 getDynamicSrc 动态获取
        };
      }

      return result;
    }).filter(Boolean);
  }

  /**
   * 销毁WebSDK
   */
  destroy(): void {
    try {
      this.isInitializing = false;
      this.callbackCache.clear();
      this.activeRequests.clear();
      this.materialSelectorCallback = null; // 清理回调

      if (this.editorInstance) {
        this.editorInstance.destroy(false);
        this.editorInstance = null;
      }

      iceNPMManager.destroy();
      this.currentId = null;
      this.currentMode = null;
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 获取当前编辑器实例
   */
  getInstance(): AliyunVideoEditor | null {
    return this.editorInstance;
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.editorInstance !== null;
  }

  /**
   * 获取当前模式
   */
  getCurrentMode(): EditorMode | null {
    return this.currentMode;
  }
}

// 导出单例
export const iceWebSDK = new UnifiedICEWebSDK();
export default iceWebSDK;