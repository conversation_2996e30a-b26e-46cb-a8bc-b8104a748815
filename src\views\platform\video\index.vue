<script setup name="platformVideo">
import { listMusetalk, delMusetalk, getAvailableModels } from "@/api/platform/video";
import { ref, getCurrentInstance, watch, onMounted, computed } from 'vue';
import AudioPlayer from './components/AudioPlayer.vue';
import VersionSelector from './components/VersionSelector.vue';
import LazyLoad from '@/components/LazyLoad/index.vue'
import LazyMedia from '@/components/LazyLoad/LazyMedia.vue'
import {Search, Refresh, Plus, Delete, VideoCamera, Loading} from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const { video_status } = proxy.useDict("video_status");
const total = ref(0);
const loading = ref(false);
const loadingMore = ref(false);
const hasMore = ref(true);
const musetalkList = ref([]);
const isPlaying = ref([]);
const progress = ref([]);
const selectedIds = ref([]);
const isAllSelected = computed({
  get() {
    return musetalkList.value.length > 0 && selectedIds.value.length === musetalkList.value.length;
  },
  set(val) {
    if (val) {
      selectedIds.value = musetalkList.value.map(item => item.id);
    } else {
      selectedIds.value = [];
    }
  }
});
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  number: null,
  status: null
});
const isInitialLoad = ref(true); // 懒加载
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  musetalkList.value = []; // 清空数据
  hasMore.value = true;
  isInitialLoad.value = true;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 获取列表数据 - 支持懒加载
function getList(isLoadMore = false) {
  if (isLoadMore) {
    loadingMore.value = true;
  } else {
    loading.value = true;
  }
  
  console.log('getList called with params:', queryParams.value, 'isLoadMore:', isLoadMore);
  
  listMusetalk(queryParams.value).then(response => {
    console.log('请求参数:', queryParams.value);
    console.log('返回数据:', response);
    const newData = response.rows || [];
    
    if (isLoadMore) {
      // 懒加载：追加数据，避免重复添加相同ID的数据
      const existingIds = new Set(musetalkList.value.map(item => item.id));
      const filteredNewData = newData.filter(item => !existingIds.has(item.id));
      musetalkList.value = [...musetalkList.value, ...filteredNewData];
      loadingMore.value = false;
      console.log('懒加载追加数据:', filteredNewData.length, '条');
    } else {
      // 首次加载或搜索：替换数据
      musetalkList.value = newData;
      loading.value = false;
      isInitialLoad.value = false;
      console.log('首次加载数据:', newData.length, '条');
    }
    
    total.value = response.total;
    hasMore.value = musetalkList.value.length < total.value;
    
    // 初始化播放状态 - 只为新增的数据初始化
    if (isLoadMore) {
      const newItemsCount = newData.filter(item => !isPlaying.value[musetalkList.value.findIndex(listItem => listItem.id === item.id)]);
      // 为新增项添加播放状态
      for (let i = 0; i < newData.length; i++) {
        isPlaying.value.push(false);
        progress.value.push(0);
      }
    } else {
      const currentLength = musetalkList.value.length;
      isPlaying.value = Array(currentLength).fill(false);
      progress.value = Array(currentLength).fill(0);
    }
    
    console.log('当前列表长度:', musetalkList.value.length, '总数:', total.value, '是否有更多:', hasMore.value);
  }).catch((error) => {
    console.error('获取列表失败:', error);
    if (isLoadMore) {
      loadingMore.value = false;
    } else {
      loading.value = false;
    }
  });
}

// 懒加载更多数据
function loadMore() {
  if (loadingMore.value || !hasMore.value || loading.value) return;
  console.log('触发懒加载, 当前页:', queryParams.value.pageNum, '下一页:', queryParams.value.pageNum + 1);
  queryParams.value.pageNum += 1;
  getList(true);
}

// 删除处理函数（支持单个和批量）
function handleDelete(row) {
  const _taskIds = row?.id || selectedIds.value;
  proxy.$modal.confirm('是否确认删除任务编号为"' + _taskIds + '"的数据项？').then(function() {
    return delMusetalk(_taskIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功！");
  }).catch(() => {});
}

// 多选框选中数据
function handleSelectionChange(checked, row) {
  if (checked) {
    selectedIds.value.push(row.id);
  } else {
    const index = selectedIds.value.indexOf(row.id);
    if (index > -1) {
      selectedIds.value.splice(index, 1);
    }
  }

}

// 批量删除直接调用handleDelete
const handleBatchDelete = () => {
  if (!selectedIds.value.length) {
    proxy.$modal.msgError("请选择要删除的数据");
    return;
  }
  handleDelete();
};

function updateIsPlaying(index, value) {
  isPlaying.value[index] = value;
}

function updateProgress(index, value) {
  progress.value[index] = value;
}

// 版本选择对话框状态
const versionDialogVisible = ref(false);

// 打开版本选择对话框
function handleAddWithVersion() {
  versionDialogVisible.value = true;
}

// 处理版本选择
function handleVersionSelected(data) {
  proxy.$router.push({ path: data.path });
}

// 添加模型列表状态和获取模型名称的方法
const modelList = ref([]);

// 获取模型名称的方法
function getModelName(modelCode) {
  if (!modelCode) return '未知模型';
  const model = modelList.value.find(m => m.modelCode === modelCode);
  return model ? model.modelName : modelCode;
}

// 添加媒体加载跟踪
const loadedMediaUrls = ref(new Set());
const mediaLoadCount = ref(0);

// 跟踪媒体加载的函数
function trackMediaLoad(url, type) {
  if (!loadedMediaUrls.value.has(url)) {
    loadedMediaUrls.value.add(url);
    mediaLoadCount.value++;
    console.log(`媒体加载: ${type} - ${url}`);
    console.log(`当前已加载媒体数量: ${mediaLoadCount.value}`);
    console.log(`已加载的URL列表:`, Array.from(loadedMediaUrls.value));
  } else {
    console.warn(`重复加载媒体: ${type} - ${url}`);
  }
}

onMounted(() => {
  getList();
  // 加载模型列表
  getAvailableModels().then(res => {
    if (res.code === 200) {
      modelList.value = res.data || [];
    }
  }).catch(err => {
    console.error('加载模型列表失败:', err);
  });

});

// 监听列表数据变化，重置选择状态
watch(() => musetalkList.value, () => {
  selectedIds.value = [];
}, { deep: true });
</script>

<template>
  <div class="synthesis-container">
    <div class="query-container">
      <el-card class="query-card" shadow="hover">
        <template #header>
          <div class="query-header"><div class="query-title"><el-icon><search /></el-icon><span>搜索</span></div></div>
        </template>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px">
          <div class="form-container">
            <el-form-item label="视频名称" prop="number">
              <el-input v-model="queryParams.number" placeholder="请输入视频名称" clearable prefix-icon="search" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable style="width: 220px">
                <el-option v-for="dict in video_status" :key="dict.value" :label="dict.label" :value="dict.value">
                  <div class="status-option">
                    <div class="status-indicator"></div>
                    <span>{{ dict.label }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <div class="query-buttons">
              <el-button type="primary" :icon="Search" @click="handleQuery">搜 索</el-button>
              <el-button type="info" :icon="Refresh" @click="resetQuery">重 置</el-button>
            </div>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-toolbar">
      <div class="left-actions">
        <el-button type="primary" class="action-btn create-btn" @click="handleAddWithVersion"><el-icon><plus /></el-icon> 视频合成</el-button>
        <el-button type="danger" class="action-btn delete-btn" :disabled="!selectedIds.length" @click="handleBatchDelete">
          <el-icon><delete /></el-icon> 批量删除
          <el-tag v-if="selectedIds.length" effect="dark" class="delete-count">{{selectedIds.length}}</el-tag>
        </el-button>
      </div>
      <div class="right-actions">
        <el-checkbox v-model="isAllSelected" @change="handleSelectAll" class="select-all">全选</el-checkbox>
        <el-divider direction="vertical" />
        <div class="data-summary">共 <span class="data-count">{{ total }}</span> 条数据</div>
      </div>
    </div>

    <!-- 内容区域 - 使用懒加载组件 -->
    <div class="tasks-container">
      <!-- 初次加载时显示骨架屏 -->
      <template v-if="loading">
        <div v-for="n in queryParams.pageSize" :key="'skeleton-init-' + n" class="task-row">
          <el-skeleton :rows="4" animated />
        </div>
      </template>
      <!-- 有数据时使用懒加载 -->
      <template v-else>
        <LazyLoad v-if="musetalkList.length > 0" :loading="loadingMore" :has-more="hasMore" @load-more="loadMore">
          <!-- 任务横向列表 -->
          <div class="task-row" v-for="(item, index) in musetalkList" :key="item.id">
            <div class="task-header">
              <div class="task-title">
                <el-checkbox :model-value="selectedIds.includes(item.id)" @change="(val) => handleSelectionChange(val, item)">
                  <span class="task-number"># {{ item.number }}</span>
                </el-checkbox>
                <dict-tag :options="video_status" :value="item.status" class="task-status"/>
              </div>
              <div class="task-actions">
                <el-button type="danger" size="small" link @click="handleDelete(item)">
                  <el-icon><delete /></el-icon>删除
                </el-button>
              </div>
            </div>

            <!-- 任务内容区域 -->
            <div class="task-content">
              <!-- 形象素材视频区域 -->
              <div class="material-section image-material">
                <div class="section-header">
                  <el-icon class="section-icon"><picture /></el-icon>
                  <span class="section-title">形象素材</span>
                </div>
                <div class="material-preview">
                  <div class="video-container">
                    <LazyMedia
                      v-if="item.drivenVideo"
                      :key="`driven-${item.id}`"
                      :src="item.drivenVideo"
                      type="video"
                      controls
                      class="material-video"
                      preload="metadata"
                      :mimeType="'video/mp4'"
                      @click.stop
                    >
                      您的浏览器不支持视频播放
                    </LazyMedia>
                  </div>
                </div>
              </div>

              <!-- 音频素材区域 -->
              <div class="material-section audio-material">
                <div class="section-header">
                  <el-icon class="section-icon"><headphone /></el-icon>
                  <span class="section-title">音频素材</span>
                </div>
                <div class="material-preview">
                  <audioPlayer
                    :key="`audio-${item.id}`"
                    :audioSrc="item.drivenAudio"
                    :index="index"
                    @update:isPlaying="(val) => updateIsPlaying(index, val)"
                    @update:progress="(val) => updateProgress(index, val)"
                    class="audio-player-compact"
                  />
                </div>

                <!-- 任务信息 -->
                <div class="task-info-inline">
                  <div class="info-item">
                    <span class="info-label">合成信息:</span>
                    <span >
                      <span class="info-segment">版本: {{ item.version || '未知' }}</span>
                      <span class="info-segment" v-if="item.bboxShiftValue !== null && item.bboxShiftValue !== undefined" style="margin-left: 30px;">
                        阈值: {{ item.bboxShiftValue }}
                      </span>
                      <span class="info-segment" v-if="item.model" style="margin-left: 25px;">
                        模型: {{ getModelName(item.model) }}
                      </span>
                    </span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">操作人:</span>
                    <span class="info-value">{{ item.createBy || 'admin' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">操作时间:</span>
                    <span class="info-value">{{ parseTime(item.createdAt) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">完成时间:</span>
                    <span class="info-value">
                      {{ item.completeAt ? parseTime(item.completeAt) : '暂无' }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 合成结果视频区域 -->
              <div class="material-section result-material">
                <div class="section-header">
                  <el-icon class="section-icon"></el-icon>
                  <span class="section-title">合成结果</span>
                </div>
                <div class="material-preview">
                  <div class="video-container">
                    <template v-if="item.status === 1 || item.status === 2">
                      <div class="processing-state">
                        <el-icon class="rotating processing-icon"><loading /></el-icon>
                        <span class="processing-text">{{ item.status === 1 ? '等待处理' : '正在生成' }}</span>
                      </div>
                    </template>
                    <template v-else>
                      <LazyMedia
                        v-if="item.resultVideo"
                        :key="`result-${item.id}`"
                        :src="item.resultVideo"
                        type="video"
                        controls
                        class="material-video result-video"
                        preload="metadata"
                        :mimeType="'video/mp4'"
                        @click.stop
                      >
                        您的浏览器不支持视频播放
                      </LazyMedia>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 懒加载更多时底部骨架屏 -->
          <template #loading>
            <div v-for="n in queryParams.pageSize" :key="'skeleton-more-' + n" class="task-row">
              <el-skeleton :rows="4" animated />
            </div>
          </template>
          <template #no-more>
            <div class="custom-no-more">
              已经到底了，没有更多了
            </div>
          </template>
        </LazyLoad>
        <!-- 暂无数据显示区域 -->
        <template v-if="musetalkList.length === 0 && !loading">
          <div class="empty-data">
            <el-empty description="暂无数据" :image-size="120">
              <template #image><el-icon class="empty-icon"><video-camera /></el-icon></template>
              <template #description><p>没有找到符合条件的视频合成任务</p></template>
              <el-button type="primary" @click="handleAddWithVersion"><el-icon><plus /></el-icon>创建视频合成任务</el-button>
            </el-empty>
          </div>
        </template>
      </template>
    </div>

    <!-- 版本选择组件 -->
    <VersionSelector v-model:visible="versionDialogVisible" @version-selected="handleVersionSelected" />
  </div>
</template>

<style lang="scss" scoped>
.synthesis-container {
  padding: 20px;
  overflow: visible; // 确保悬停动画不被裁剪
}

// 查询表单样式优化
.query-container {
  margin-bottom: 20px;
  
  .query-card {
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  .query-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .query-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: bold;
      color: #409EFF;
    }
  }
  
  .form-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
  }
  
  .query-buttons {
    display: flex;
    gap: 10px;
    margin-left: auto;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 10px;
    margin-right: 20px;
    
    .el-input__wrapper,
    .el-select .el-input__wrapper {
      box-shadow: 0 0 0 1px #dcdfe6 inset;
      border-radius: 4px;
      transition: all 0.2s;
      
      &:hover, &:focus {
        box-shadow: 0 0 0 1px #409EFF inset;
      }
    }
  }
}

.action-toolbar {
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .left-actions {
    display: flex;
    gap: 10px;
    
    .action-btn {
      display: flex;
      align-items: center;
      gap: 5px;
      border-radius: 4px;
      padding: 10px 15px;
      font-weight: 500;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      &.create-btn {
        background: linear-gradient(to right, #409EFF, #53a8ff);
        border: none;
      }
      
      &.delete-btn {
        position: relative;
        
        .delete-count {
          position: absolute;
          top: -8px;
          right: -8px;
          border-radius: 10px;
          font-size: 12px;
          padding: 0 6px;
          height: 18px;
          line-height: 18px;
          background-color: #fff;
          color: #F56C6C;
          border: 1px solid currentColor;
        }
      }
    }
  }
  
  .right-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .select-all {
      margin-right: 5px;
      font-size: 14px;
      
      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #409EFF;
        border-color: #409EFF;
      }
    }
    
    .data-summary {
      color: #606266;
      font-size: 14px;
      
      .data-count {
        color: #409EFF;
        font-weight: bold;
        padding: 0 4px;
      }
    }
  }
}

// 状态选择器样式
.status-option {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
}

// 任务容器样式 - 横向布局
.tasks-container {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  padding: 8px 8px 8px 0; // 增加上下内边距，防止悬停动画被裁剪

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.task-row {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin: 8px 0 16px 0; // 增加上边距，为悬停动画预留空间
  padding: 10px 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f0f0f0;
  position: relative; // 确保层级正确
  z-index: 1; // 基础层级

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: #409EFF;
    z-index: 10; // 悬停时提升层级，确保不被遮挡
  }

  &:first-child {
    margin-top: 0; // 第一个元素不需要上边距
  }

  &:last-child {
    margin-bottom: 8px; // 最后一个元素保持底部间距
  }
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  min-height: 60px; // 确保头部高度一致

  .task-title {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;

    :deep(.el-checkbox) {
      .el-checkbox__input {
        transform: scale(1.2); // 稍微放大复选框
      }

      .el-checkbox__label {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        padding-left: 8px;
      }
    }

    .task-number {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }

    .task-status {
      margin-left: 16px;

      :deep(.el-tag) {
        font-size: 13px;
        padding: 4px 12px;
        border-radius: 16px;
        font-weight: 500;
      }
    }
  }

  .task-actions {
    display: flex;
    gap: 8px;
    align-items: center;

    .el-button {
      height: 36px;
      padding: 8px 16px;
      border-radius: 18px;
      font-size: 13px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
      }
    }
  }
}

.task-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr; // 三列等宽布局：形象素材 | 音频素材 | 结果视频
  gap: 24px;
  align-items: start; // 改回start，让音频区域自然高度
  position: relative; // 为子元素的层级提供参考
  overflow: visible; // 确保悬停动画不被裁剪

  @media (max-width: 1200px) {
    gap: 20px;

    .video-container, .processing-state {
      height: 200px; // 在中等屏幕上稍微减小
    }
  }

  @media (max-width: 1024px) {
    grid-template-columns: 1fr 1fr; // 两列布局
    gap: 18px;

    .audio-material {
      grid-column: 1 / -1; // 音频素材占满整行
      margin-top: 16px;
    }

    .video-container, .processing-state {
      height: 180px;
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr; // 单列布局
    gap: 16px;

    .video-container, .processing-state {
      height: 200px; // 在移动端保持合适大小
    }
  }

  @media (max-width: 480px) {
    gap: 14px;

    .video-container, .processing-state {
      height: 180px;
    }
  }
}

// 素材区域样式
.material-section {
  background: #fafbfc;
  border-radius: 12px; // 增加圆角
  padding: 18px; // 增加内边距
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
  height: 300px;

  // 只对视频区域使用flex布局
  &.image-material,
  &.result-material {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  // 添加微妙的渐变背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  &:hover {
    background: #f8f9fa;
    border-color: #e4e7ed;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    z-index: 5; // 悬停时提升层级

    &::before {
      opacity: 1;
    }
  }

  &.image-material {
    .section-icon {
      color: #409EFF;
      background: rgba(64, 158, 255, 0.1);
      padding: 4px;
      border-radius: 6px;
    }
  }

  &.audio-material {
    .section-icon {
      color: #67C23A;
      background: rgba(103, 194, 58, 0.1);
      padding: 4px;
      border-radius: 6px;
    }
  }

  &.result-material {
    .section-icon {
      color: #E6A23C;
      background: rgba(230, 162, 60, 0.1);
      padding: 4px;
      border-radius: 6px;
    }
  }

  &.task-info-section {
    .section-icon {
      color: #909399;
      background: rgba(144, 147, 153, 0.1);
      padding: 4px;
      border-radius: 6px;
    }
  }
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px; // 增加底部间距
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);

  .section-icon {
    font-size: 18px; // 增加图标大小
    transition: transform 0.2s ease;
  }

  .section-title {
    font-size: 15px; // 稍微增加标题字体大小
    font-weight: 600;
    color: #303133;
    letter-spacing: 0.5px;
  }
}

.material-section:hover .section-header .section-icon {
  transform: scale(1.1);
}

.material-preview {
  position: relative;
}

// 只对视频预览区域使用flex布局
.image-material .material-preview,
.result-material .material-preview {
  flex: 1; // 让预览区域占据剩余空间
  display: flex;
  flex-direction: column;
}

.video-container {
  position: relative;
  width: 100%;
  height: 220px; // 调整为统一的合适高度
  background: #000;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);
    transform: translateY(-2px);
    z-index: 3; // 悬停时提升层级
  }

  .material-video {
    width: 100%;
    height: 100%;
    object-fit: contain; // 改为contain确保完整显示，不裁剪
    display: block;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #000; // 添加黑色背景

    &.result-video {
      object-fit: contain;
    }

    &:hover {
      filter: brightness(1.05);
    }

    &:focus {
      outline: 2px solid #409EFF;
      outline-offset: 2px;
    }
  }
}

.processing-state {
  height: 220px; // 保持与video-container相同高度
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #606266;
  border-radius: 10px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);

  .processing-icon {
    font-size: 36px; // 增加处理图标大小
    margin-bottom: 16px;
    color: #409EFF;
  }

  .processing-text {
    font-size: 16px; // 增加文字大小
    font-weight: 500;
  }
}



// 任务信息内联样式
.task-info-inline {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      color: #606266;
      font-weight: 500;
      margin-right: 8px;
      min-width: 60px;
      flex-shrink: 0;
    }

    .info-value {
      color: #303133;
      font-weight: 400;
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .info-segment {
        display: inline-flex;
        align-items: center;
        font-size: 12px;
        color: #606266;
        padding: 2px 8px;
        white-space: nowrap;
      }
    }
  }
}

.task-details {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .detail-label {
      font-size: 12px;
      color: #909399;
      font-weight: 500;
    }

    .detail-value {
      font-size: 14px;
      color: #303133;
      display: flex;
      align-items: center;
      gap: 4px;

      .el-icon {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.value-tag {
  font-size: 14px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;

  &.text-danger {
    color: #f56c6c;
    background: rgba(245, 108, 108, 0.1);
  }
  &.text-warning {
    color: #e6a23c;
    background: rgba(230, 162, 60, 0.1);
  }
  &.text-success {
    color: #67c23a;
    background: rgba(103, 194, 58, 0.1);
  }
}

// 自定义加载样式
.custom-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #909399;
  font-size: 14px;

  .rotating {
    font-size: 16px;
    color: #409EFF;
  }
}

// 自定义没有更多数据样式
.custom-no-more {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}



// 更新暂无数据的样式
.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 20px 0;

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-3px);
  }

  :deep(.el-empty) {
    padding: 50px;

    .el-empty__description {
      margin-top: 24px;

      p {
        color: #606266;
        font-size: 18px;
        margin-bottom: 16px;
        font-weight: 500;
      }
    }

    .el-button {
      padding: 14px 28px;
      font-size: 15px;
      border-radius: 8px;
      font-weight: 500;
      background: linear-gradient(to right, #409EFF, #53a8ff);
      border: none;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
      }
    }
  }

  .empty-icon {
    font-size: 100px;
    color: #409EFF;
    animation: float 4s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(64, 158, 255, 0.2));
  }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

// 美化后的版本选择对话框样式
:deep(.version-dialog) {
  border-radius: 12px;
  overflow: hidden;
  
  .el-dialog__header {
    padding: 20px 24px;
    margin: 0;
    border-bottom: 1px solid #f0f0f0;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .el-dialog__headerbtn {
    top: 20px;
    right: 20px;
  }
  
  .el-dialog__body {
    padding: 24px;
  }
  
  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 15px 24px;
  }
}
</style>
