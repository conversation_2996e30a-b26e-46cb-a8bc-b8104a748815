/**
 * 通用字典映射：业务类型、状态、来源（中文）
 */
export const BusinessTypeMap: Record<string, string> = {
  general: '通用',
  opening: '片头',
  ending: '片尾',
  watermark: '水印',
  subtitles: '字幕'
}

export const StatusMap: Record<string, string> = {
  Init: '初始',
  Preparing: '准备中',
  PrepareFail: '准备失败',
  Normal: '正常'
}

export const SourceMap: Record<string, string> = {
  oss: 'OSS存储',
  vod: '视频点播',
  live: '视频直播',
  general: '其他'
}

/**
 * 字典转下拉选项工具
 */
export function mapToOptions(map: Record<string, string>, withAll = true) {
  const options = Object.entries(map).map(([value, label]) => ({ value, label }))
  return withAll ? [{ value: '', label: '全部' }, ...options] : options
}
/**
 * 本文件根据阿里云智能媒体服务(ICE)的 GetMediaInfo 接口的实际JSON响应结构进行定义。
 * 更新于：2025-06-28，基于用户提供的最新API响应。
 */
import type { AxiosPromise } from 'axios';

/**
 * 查询媒体信息接口的请求参数类型
 */
export interface MediaInfoQuery {
  mediaId?: string;
  inputURL?: string;
  outputType?: 'oss' | 'cdn';
  returnDetailedInfo?: string; // JSON string
}

// -------------------- Response Data Structures --------------------

/**
 * API响应的根对象
 */
export interface MediaInfoResponse {
  RequestId: string;
  MediaInfo: MediaPayload;
}

/**
 * 媒体信息的核心载荷
 * @description Corresponds to the `MediaInfo` object in the JSON response.
 */
export interface MediaPayload {
  MediaId: string;
  MediaBasicInfo: MediaBasicInfo;
  FileInfoList: FileInfo[];
  AiRoughData?: AiRoughData;
  PostProcessInfo?: object;
}



/**
 * 雪碧图（缩略图）信息
 */
export interface SpriteImage {
  bucket: string;
  count: number;
  iceJobId: string;
  location: string;
  snapshotRegular: string;
  spriteRegular: string;
  templateId: string;
  tileCount: number;
}





/**
 * 音频流信息
 */
export interface AudioStreamInfo {
  Index: number;
  CodecName: string;
  CodecLongName: string;
  CodecTimeBase?: string;
  CodecTagString: string;
  CodecTag: string;
  Profile?: string;
  SampleFmt: string;
  SampleRate: number;
  Channels: number;
  ChannelLayout: string;
  Timebase: string;
  StartTime: number;
  Duration: number;
  Bitrate: number;
  Fps?: number;
  NumFrames?: number;
  Lang: string;
}

/**
 * 视频流信息
 */
export interface VideoStreamInfo {
  Index: number;
  CodecName: string;
  CodecLongName: string;
  Profile: string;
  CodecTimeBase?: string;
  CodecTagString: string;
  CodecTag: string;
  Width: number;
  Height: number;
  HasBFrames: number;
  Sar: string;
  Dar: string;
  PixFmt: string;
  Level: number;
  Fps?: number;
  AvgFPS?: string;
  Timebase: string;
  StartTime: number;
  Duration: number;
  Bitrate: number;
  NumFrames: number;
  Lang: string;
  Rotate?: number;
  Nb_frames?: number;
}

/**
 * 字幕流信息
 */
export interface SubtitleStreamInfo {
  Index: number;
  CodecName: string;
  CodecLongName: string;
  CodecTimeBase?: number;
  CodecTagString: string;
  CodecTag: string;
  Timebase: number;
  StartTime: number;
  Duration: number;
  Lang: string;
}

/**
 * AI原始数据
 */
export interface AiRoughData {
  Result: string;
  Status: string;
  SaveType: string;
  AiCategory: string;
  AiJobId: string;
  StandardSmartTagJob?: StandardSmartTagJob;
}

/**
 * AI标准智能标签任务信息
 */
export interface StandardSmartTagJob {
  AiJobId: string;
  Status: string;
  ResultUrl: string;
  Results: AiResult[];
}

/**
 * AI任务结果
 */
export interface AiResult {
  Data: string; // JSON string
  Type: string;
}

/**
 * @deprecated 后端 AjaxResult.data 已被解析为 MediaInfoResponse，请直接使用 MediaInfoResponse
 */
export type MediaInfoResult = AxiosPromise<MediaInfoResponse>;

// =========================================================================
// GetEditingProjectMaterials 接口相关类型定义
// =========================================================================

/**
 * 获取剪辑工程关联素材请求参数
 */
export interface GetEditingProjectMaterialsRequest {
  /**
   * 云剪辑工程ID（必填）
   */
  ProjectId: string;
}

/**
 * 获取剪辑工程关联素材响应
 */
export interface GetEditingProjectMaterialsResponse {
  /**
   * 请求ID
   */
  RequestId: string;

  /**
   * 项目ID号
   */
  ProjectId: string;

  /**
   * 符合要求的媒资集合
   */
  MediaInfos: ProjectMediaInfo[];

  /**
   * 直播流关联素材
   */
  LiveMaterials: LiveMaterial[];

  /**
   * 剪辑工程关联素材。直播工程在直播结束后会关联到一个普通剪辑工程
   */
  ProjectMaterials: string[];
}

/**
 * 项目媒资信息
 */
export interface ProjectMediaInfo {
  /**
   * 媒资ID
   */
  MediaId: string;

  /**
   * 媒资基础信息
   */
  MediaBasicInfo: ProjectMediaBasicInfo;

  /**
   * 文件信息列表
   */
  FileInfoList: ProjectFileInfo[];
}

/**
 * 项目媒资基础信息
 */
export interface ProjectMediaBasicInfo {
  /**
   * 媒资ID
   */
  MediaId: string;

  /**
   * 待注册的媒资在相应系统中的地址
   */
  InputURL: string;

  /**
   * 媒资媒体类型
   */
  MediaType: string;

  /**
   * 媒资业务类型
   */
  BusinessType: string;

  /**
   * 来源
   */
  Source: string;

  /**
   * 标题
   */
  Title: string;

  /**
   * 内容描述
   */
  Description: string;

  /**
   * 分类
   */
  Category: string;

  /**
   * 标签
   */
  MediaTags: string;

  /**
   * 封面地址
   */
  CoverURL: string;

  /**
   * 用户数据
   */
  UserData: string;

  /**
   * 截图
   */
  Snapshots: string | null;

  /**
   * 资源状态
   */
  Status: string;

  /**
   * 转码状态
   * - TranscodeSuccess：转码完成
   * - TranscodeFailed：转码失败
   * - Init：初始化
   * - Transcoding：转码中
   */
  TranscodeStatus: 'TranscodeSuccess' | 'TranscodeFailed' | 'Init' | 'Transcoding';

  /**
   * 媒资创建时间
   */
  CreateTime: string;

  /**
   * 媒资修改时间
   */
  ModifiedTime: string;

  /**
   * 媒资删除时间
   */
  DeletedTime: string;

  /**
   * 雪碧图
   */
  SpriteImages: string | null;
}

/**
 * 项目文件信息
 */
export interface ProjectFileInfo {
  /**
   * 文件基础信息
   */
  FileBasicInfo: ProjectFileBasicInfo;
}

/**
 * 项目文件基础信息，包含时长，大小等
 */
export interface ProjectFileBasicInfo {
  /**
   * 文件名
   */
  FileName: string;

  /**
   * 文件状态
   */
  FileStatus: string;

  /**
   * 文件类型
   */
  FileType: string;

  /**
   * 文件大小（字节）
   */
  FileSize: number;

  /**
   * 文件OSS地址
   */
  FileUrl: string;

  /**
   * 文件存储区域
   */
  Region: string;

  /**
   * 封装格式
   */
  FormatName: string;

  /**
   * 时长
   */
  Duration: number;

  /**
   * 码率
   */
  Bitrate: number;

  /**
   * 宽
   */
  Width: number;

  /**
   * 高
   */
  Height: number;
}

/**
 * 直播流关联素材
 */
export interface LiveMaterial {
  /**
   * 直播流配置
   */
  LiveStreamConfig: LiveStreamConfig;
}

/**
 * 直播流配置
 */
export interface LiveStreamConfig {
  /**
   * 直播播流应用名
   */
  AppName: string;

  /**
   * 直播播流流名
   */
  StreamName: string;

  /**
   * 直播播流域名
   */
  DomainName: string;

  /**
   * 直播播流地址
   */
  LiveUrl: string;
}

// =========================================================================
// MediaUpload 接口相关类型定义
// =========================================================================

/**
 * 获取媒资上传地址和凭证的请求参数
 */
export interface MediaUploadRequest {
  AppId?: string;           // 应用ID
  EntityId?: string;       // 实体ID
  FileInfo: {             // 文件信息
    Type: 'video' | 'image' | 'audio' | 'text' | 'other'; // 文件类型
    Name: string;         // 文件名称不带扩展名
    Size?: number;       // 文件大小
    Ext: string;         // 文件扩展名
  };
  UserData?: {           // 用户自定义数据对象
    source?: string;
    category?: string;
    uploadTime?: string;
    [key: string]: any;  // 允许其他自定义字段
  };
  UploadTargetConfig?: {      // 目标存储地址
    StorageType: 'oss';       // 存储类型
    StorageLocation: string;  // 存储位置
  };
  MediaMetaData?: {     // 媒资元数据
    Title: string;
    Description?: string;
    CateId?: number;
    Tags?: string;
    BusinessType: string;
    CoverURL?: string;
    DynamicMetaData?: string;
  };                
  PostProcessConfig?: { // 后处理配置
    ProcessType: 'Workflow';
    ProcessID: string;
  };
}

/**
 * 媒资上传响应结构
 */
export interface MediaUploadResponse {
  signedUrl: string;        // 签名上传地址
  uniqueObjectKey: string;  // 唯一对象键
  mediaId: string;          // 媒资ID
  bucket: string;           // 存储桶名称
  endpoint: string;         // 存储服务端点
}

/**
 * 媒资注册请求参数
 */
export interface MediaRegisterRequest {
  /**
   * 待注册的媒资在相应系统中的地址（必填）
   * OSS地址支持两种格式：
   * - http(s)://example-bucket.oss-cn-shanghai.aliyuncs.com/example.mp4
   * - oss://example-bucket/example.mp4
   * VOD媒资：vod://***20b48fb04483915d4f2cd8ac****
   */
  InputURL: string;

  /**
   * 媒资媒体类型
   * - "image" 图片
   * - "video" 视频  
   * - "audio" 音频
   * - "text" 文字
   */
  MediaType?: 'image' | 'video' | 'audio' | 'text';

  /**
   * 媒资业务类型
   * - "subtitles" 字幕
   * - "watermark" 水印
   * - "opening" 片头/开场
   * - "ending" 片尾
   * - "general" 通用
   */
  BusinessType?: 'subtitles' | 'watermark' | 'opening' | 'ending' | 'general';

  /**
   * 标题，若不提供，根据日期自动生成默认title
   * 长度不超过128字节，UTF8编码
   */
  Title?: string;

  /**
   * 内容描述
   * 长度不超过1024字节，UTF8编码
   */
  Description?: string;

  /**
   * 标签
   * 最多不超过16个标签，多个用逗号分隔
   * 单个标签不超过32字节，UTF8编码
   */
  MediaTags?: string;

  /**
   * 封面地址
   * 长度不超过128字节，UTF8编码
   */
  CoverURL?: string;

  /**
   * 用户数据，支持自定义回调地址配置
   * 长度不超过1024字节，UTF8编码，Json格式
   */
  UserData?: string;

  /**
   * 是否覆盖已注册媒资，默认false
   * - true：如果inputUrl已注册，删除原有媒资并注册新媒资
   * - false：如果inputUrl已注册则不予注册新媒资
   */
  Overwrite?: boolean;

  /**
   * 客户端token，32位UUID，保证请求幂等性
   */
  ClientToken?: string;

  /**
   * 注册配置
   * 默认为媒资生成雪碧图和截图，可通过配置控制
   */
  RegisterConfig?: string;

  /**
   * 分类ID
   */
  CateId?: number;

  /**
   * 工作流ID
   */
  WorkflowId?: string;

  /**
   * 自定义ID，仅支持字母、数字、横线、下划线，长度6-64位
   * 需保证用户维度唯一
   */
  ReferenceId?: string;

  /**
   * 智能标签模板ID
   * - S00000101-300080：包含NLP内容理解功能的系统模板
   * - S00000103-000001：包含NLP内容理解功能和所有标签能力
   * - S00000103-000002：包含所有标签能力，不包含NLP内容理解功能
   */
  SmartTagTemplateId?: string;
}

/**
 * 媒资注册响应结构
 */
export interface MediaRegisterResponse {
  /**
   * 请求ID
   */
  RequestId: string;

  /**
   * IMS媒资ID
   */
  MediaId: string;
}

/**
 * 一体化上传与注册接口响应类型
 * 根据后端接口文档定义的实际响应结构
 */
export interface UploadAndRegisterResponse {
  /**
   * 响应状态码，200表示成功
   */
  code: number;

  /**
   * 响应消息
   */
  msg: string;

  /**
   * 阿里云ICE返回的数据
   */
  data: {
    /**
     * 媒资ID - 重要：用于后续媒资操作的唯一标识
     */
    MediaId: string;

    /**
     * 阿里云请求ID，用于问题追踪
     */
    RequestId: string;
  };

  /**
   * 文件在OSS中的完整地址
   */
  ossUrl: string;

  /**
   * 文件在ICE媒资库中的路径
   */
  filePath: string;

  /**
   * 框架返回的上传路径
   */
  uploadedPath: string;

  /**
   * 原始文件名
   */
  fileName: string;

  /**
   * 文件大小（字节）
   */
  fileSize: number;

  /**
   * 文件分类
   */
  category: string;
}

// =========================================================================
// BatchGetMediaInfos 接口相关类型定义
// =========================================================================

/**
 * 批量获取媒资信息请求参数
 */
export interface BatchGetMediaInfosRequest {
  /**
   * 所有待查询的媒资 ID，以逗号分隔
   * 示例: "mediaId1,mediaId2,mediaId3"
   */
  MediaIds?: string;

  /**
   * 批量查询的媒资额外信息，默认只返回 BasicInfo
   * 额外文件信息内容包括：
   * - FileInfo: 文件信息
   * - DynamicMetaData: 动态元数据
   * 多个类型用逗号分隔，例如：FileInfo,DynamicMetaData
   */
  AdditionType?: string;
}

/**
 * 批量获取媒资信息响应
 */
export interface BatchGetMediaInfosResponse {
  /** 请求ID */
  RequestId: string;
  /** 媒资信息列表 */
  MediaInfos: BatchMediaInfo[];
}

/**
 * 批量媒资信息 - 用于批量查询接口
 */
export interface BatchMediaInfo {
  /** 媒资ID */
  MediaId: string;
  /** 媒资基础信息 */
  MediaBasicInfo: MediaBasicInfo;
  /** 文件信息列表 */
  FileInfoList?: FileInfo[];
}

/**
 * 批量查询的媒资基础信息
 */
export interface BatchMediaBasicInfo {
  /**
   * 媒资ID
   */
  MediaId: string;

  /**
   * 待注册的媒资在相应系统中的地址
   */
  InputURL: string;

  /**
   * 媒资媒体类型
   * - Image: 图片
   * - Video: 视频
   * - Audio: 音频
   * - Text: 文字
   */
  MediaType: 'Image' | 'Video' | 'Audio' | 'Text';

  /**
   * 媒资业务类型
   */
  BusinessType: string;

  /**
   * 来源
   * - OSS: 对象存储
   * - vod: 视频点播
   */
  Source: 'OSS' | 'vod';

  /**
   * 标题
   */
  Title: string;

  /**
   * 内容描述
   */
  Description: string;

  /**
   * 分类
   */
  Category: string;

  /**
   * 标签，多个用逗号分隔
   */
  MediaTags: string;

  /**
   * 封面地址
   */
  CoverURL: string;

  /**
   * 用户数据
   */
  UserData: string;

  /**
   * 截图信息，JSON字符串格式
   */
  Snapshots: string;

  /**
   * 资源状态
   * - Normal: 正常
   */
  Status: 'Normal' | string;

  /**
   * 转码状态
   * - Init: 初始化
   * - Transcoding: 转码中
   * - TranscodeSuccess: 转码完成
   * - TranscodeFailed: 转码失败
   */
  TranscodeStatus: 'Init' | 'Transcoding' | 'TranscodeSuccess' | 'TranscodeFailed';

  /**
   * 媒资创建时间
   */
  CreateTime: string;

  /**
   * 媒资修改时间
   */
  ModifiedTime: string;

  /**
   * 媒资删除时间
   */
  DeletedTime: string;

  /**
   * 雪碧图信息，JSON字符串格式
   */
  SpriteImages: string;

  /**
   * 业务标识
   */
  Biz?: string;
}

/**
 * 批量查询的文件信息
 */
export interface BatchFileInfo {
  /**
   * 文件基础信息
   */
  FileBasicInfo: BatchFileBasicInfo;
}

/**
 * 批量查询的文件基础信息
 */
export interface BatchFileBasicInfo {
  /**
   * 文件名
   */
  FileName: string;

  /**
   * 文件状态
   * - Normal: 正常
   */
  FileStatus: 'Normal' | string;

  /**
   * 文件类型
   * - source_file: 源文件
   */
  FileType: 'source_file' | string;

  /**
   * 文件大小（字节）
   */
  FileSize: number;

  /**
   * 文件OSS地址
   */
  FileUrl: string;

  /**
   * 文件存储区域
   */
  Region: string;

  /**
   * 封装格式
   */
  FormatName: string;

  /**
   * 时长（秒）
   */
  Duration: number;

  /**
   * 码率
   */
  Bitrate: number;

  /**
   * 宽度（像素）
   */
  Width: number;

  /**
   * 高度（像素）
   */
  Height: number;
}

/**
 * 媒资状态枚举
 */
export enum MediaStatus {
  /**
   * 正常
   */
  NORMAL = 'Normal'
}

/**
 * 转码状态枚举
 */
export enum TranscodeStatus {
  /**
   * 初始化
   */
  INIT = 'Init',

  /**
   * 转码中
   */
  TRANSCODING = 'Transcoding',

  /**
   * 转码完成
   */
  TRANSCODE_SUCCESS = 'TranscodeSuccess',

  /**
   * 转码失败
   */
  TRANSCODE_FAILED = 'TranscodeFailed'
}

/**
 * 文件状态枚举
 */
export enum FileStatus {
  /**
   * 正常
   */
  NORMAL = 'Normal'
}

/**
 * 文件类型枚举
 */
export enum FileType {
  /**
   * 源文件
   */
  SOURCE_FILE = 'source_file'
}

/**
 * 媒体类型枚举
 */
export enum MediaType {
  /**
   * 图片
   */
  IMAGE = 'Image',

  /**
   * 视频
   */
  VIDEO = 'Video',

  /**
   * 音频
   */
  AUDIO = 'Audio',

  /**
   * 文字
   */
  TEXT = 'Text'
}

/**
 * 来源类型枚举
 */
export enum SourceType {
  /**
   * 对象存储
   */
  OSS = 'OSS',

  /**
   * 视频点播
   */
  VOD = 'vod'
}


/**
 * 媒资列表基础信息查询参数 - 基于阿里云ICE ListMediaBasicInfos接口
 */
export interface MediaListQueryParams {
  /** 创建时间的开始时间，UTC时间格式：YYYY-MM-DD'T'hh:mm:ss'Z' */
  StartTime?: string;
  /** 创建时间的结束时间，UTC时间格式：YYYY-MM-DD'T'hh:mm:ss'Z' */
  EndTime?: string;
  /** 媒资媒体类型：image/video/audio/text */
  MediaType?: 'Image' | 'Video' | 'Audio' | 'Text';
  /** 媒资业务类型：subtitles/watermark/opening/ending/general */
  BusinessType?: 'subtitles' | 'watermark' | 'opening' | 'ending' | 'general';
  /** 来源：oss/vod/live/general */
  Source?: 'oss' | 'vod' | 'live' | 'general';
  /** 资源状态：Init/Preparing/PrepareFail/Normal */
  Status?: 'Init' | 'Preparing' | 'PrepareFail' | 'Normal';
  /** 分页令牌，用于表示当前调用返回读取到的位置 */
  NextToken?: string;
  /** 本次请求所返回的最大记录条数，最大值：100，默认值：10 */
  MaxResults?: number;
  /** 排序方式：desc(倒序)/asc(正序)，默认倒序 */
  SortBy?: 'desc' | 'asc';
  /** 是否包含文件基础信息 */
  IncludeFileBasicInfo?: boolean;
  /** 媒资ID */
  MediaId?: string;
}

/**
 * 媒资搜索参数
 */
export interface MediaSearchParams {
  /** 搜索关键词 */
  Keyword: string;
  /** 媒资类型筛选 */
  MediaType?: string;
  /** 分类筛选 */
  Category?: string;
  /** 页码 */
  PageNumber?: number;
  /** 每页数量 */
  PageSize?: number;
}

/**
 * 媒资基础信息 - 基于官方文档MediaBasicInfo结构
 */
export interface MediaBasicInfo {
  /** 媒资ID */
  MediaId: string;
  /** 待注册的媒资在相应系统中的地址 */
  InputURL: string;
  /** 媒资媒体类型 */
  MediaType: string;
  /** 媒资业务类型 */
  BusinessType: string;
  /** 来源 */
  Source: string;
  /** 标题 */
  Title: string;
  /** 内容描述 */
  Description: string;
  /** 分类 */
  Category: string;
  /** 标签 */
  MediaTags: string;
  /** 封面地址 */
  CoverURL: string;
  /** 用户数据 */
  UserData: string;
  /** 截图信息 */
  Snapshots: string;
  /** 资源状态 */
  Status: string;
  /** 转码状态 */
  TranscodeStatus: string;
  /** 媒资创建时间 */
  CreateTime: string;
  /** 媒资修改时间 */
  ModifiedTime: string;
  /** 媒资删除时间 */
  DeletedTime: string;
  /** 雪碧图信息 */
  SpriteImages: string;
  /** 分类ID */
  CateId?: number;
  /** 媒资所属业务 */
  Biz?: string;
  /** 媒资上传来源 */
  UploadSource?: string;
  /** 自定义ID */
  ReferenceId?: string;
}

/**
 * 文件基础信息 - 基于官方文档FileBasicInfo结构
 */
export interface FileBasicInfo {
  /** 文件名 */
  FileName: string;
  /** 文件状态 */
  FileStatus: string;
  /** 文件类型 */
  FileType: string;
  /** 文件大小（字节） */
  FileSize: string;
  /** 文件OSS地址 */
  FileUrl: string;
  /** 文件存储区域 */
  Region: string;
  /** 封装格式 */
  FormatName: string;
  /** 时长 */
  Duration: string;
  /** 码率 */
  Bitrate: string;
  /** 宽度 */
  Width: string;
  /** 高度 */
  Height: string;
  /** 文件创建时间 */
  CreateTime: string;
  /** 文件修改时间 */
  ModifiedTime: string;
}

/**
 * 文件信息
 */
export interface FileInfo {
  /** 文件基础信息 */
  FileBasicInfo: FileBasicInfo;
}

/**
 * 媒资信息 - 基于官方文档MediaInfo结构
 */
export interface MediaInfo {
  /** 媒资ID */
  MediaId: string;
  /** 媒资基础信息 */
  MediaBasicInfo: MediaBasicInfo;
  /** 文件信息列表 */
  FileInfoList?: FileInfo[];
}

/**
 * 媒资列表响应 - 基于官方文档Response结构
 */
export interface MediaListResponse {
  /** 请求ID */
  RequestId: string;
  /** 本次请求条件下的数据总量 */
  TotalCount: number;
  /** 符合要求的媒资集合 */
  MediaInfos: MediaInfo[];
  /** 分页令牌 */
  NextToken?: string;
  /** 本次请求所返回的最大记录条数 */
  MaxResults?: number;
}

// ==================== 批量媒资信息相关类型定义 ====================

/**
 * 额外信息类型枚举
 */
export type AdditionType = 'FileInfo' | 'DynamicMetaData';

/**
 * 批量媒资信息 - 用于批量查询接口
 */
export interface BatchMediaInfo {
  /** 媒资ID */
  MediaId: string;
  /** 媒资基础信息 */
  MediaBasicInfo: MediaBasicInfo;
  /** 文件信息列表 */
  FileInfoList?: FileInfo[];
}

/**
 * 批量获取媒资信息响应
 */
export interface BatchGetMediaInfosResponse {
  /** 请求ID */
  RequestId: string;
  /** 媒资信息列表 */
  MediaInfos: BatchMediaInfo[];
}
