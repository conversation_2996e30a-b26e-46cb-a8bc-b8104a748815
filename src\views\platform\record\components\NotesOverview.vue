<template>
  <div class="notes-overview">
    <!-- 粒子背景容器 -->
    <div class="particles-container" ref="particlesContainer"></div>

    <!-- 笔记展示按钮 -->
    <el-button
      @click="toggleNotesPanel"
      type="primary"
      class="notes-overview-btn"
      :class="{
        'active': isPanelOpen,
        'no-notes': !hasNotes
      }"
    >
      <el-icon size="18" class="notes-icon">
        <EditPen />
      </el-icon>
      <span class="notes-text">笔记概览</span>
      <span class="notes-count" v-if="hasNotes">{{ notesCount }}</span>
      <span class="no-notes-text" v-else>暂无</span>
      <el-icon size="14" class="arrow-icon" :class="{ 'rotated': isPanelOpen }">
        <ArrowDown />
      </el-icon>
    </el-button>

    <!-- 笔记面板 -->
    <transition name="notes-panel">
      <div v-if="isPanelOpen" class="notes-panel" @wheel="handlePanelWheel">
        <div class="notes-panel-header">
          <div class="panel-title">
            <el-icon size="20"><EditPen /></el-icon>
            <span>笔记概览</span>
          </div>
          <div class="header-actions">
            <el-button
              v-if="hasNotes"
              @click="deleteAllNotes"
              type="text"
              size="small"
              class="delete-all-btn"
              :title="'删除所有笔记'"
            >
              <el-icon size="16"><Delete /></el-icon>
              <span>清空</span>
            </el-button>
            <el-button
              @click="closePanel"
              type="text"
              size="small"
              class="close-btn"
            >
              <el-icon size="16"><Close /></el-icon>
            </el-button>
          </div>
        </div>

        <div class="notes-list" ref="notesListRef" @wheel="handleNotesListWheel">
          <div
            v-for="(note, index) in notesWithContent"
            :key="index"
            class="note-item"
            @click="scrollToConversation(note.index)"
          >
            <div class="note-header">
              <div class="speaker-info">
                <div
                  class="speaker-circle"
                  :style="{ backgroundColor: getSpeakerColor(note.speakerId) }"
                >
                  {{ note.speakerId }}
                </div>
                <span class="speaker-name">{{ note.speakerName }}</span>
              </div>
              <div class="note-actions">
                <div class="note-time">{{ formatTimestamp(note.startTime) }}</div>
                <el-button
                  @click.stop="deleteNote(note.index)"
                  type="text"
                  size="small"
                  class="delete-note-btn"
                  :title="'删除这条笔记'"
                >
                  <el-icon size="14"><Delete /></el-icon>
                </el-button>
              </div>
            </div>

            <div class="note-content">
              <div class="original-text" :title="note.text">{{ note.text }}</div>
              <div class="note-text" :title="note.notes">{{ note.notes }}</div>
            </div>
          </div>
        </div>

        <div v-if="notesWithContent.length === 0" class="empty-notes">
          <el-icon size="48" class="empty-icon"><Document /></el-icon>
          <p>暂无笔记内容</p>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted, nextTick } from 'vue'
import { EditPen, ArrowDown, Close, Document, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  conversations: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['scroll-to-conversation', 'delete-note', 'delete-all-notes', 'panel-state-change'])

// 状态管理
const isPanelOpen = ref(false)
const particlesContainer = ref(null)
const notesListRef = ref(null)
// 移除JavaScript动态更新，完全依赖CSS全局样式

// 计算有笔记的对话
const notesWithContent = computed(() => {
  return props.conversations
    .map((conversation, index) => ({ ...conversation, index }))
    .filter(conversation => conversation.notes && conversation.notes.trim())
})

// 计算笔记数量
const notesCount = computed(() => notesWithContent.value.length)

// 是否有笔记
const hasNotes = computed(() => notesCount.value > 0)

// 切换面板显示
const toggleNotesPanel = () => {
  isPanelOpen.value = !isPanelOpen.value

  // 发送面板状态变化事件
  emit('panel-state-change', isPanelOpen.value)

  if (isPanelOpen.value) {
    nextTick(() => {
      initParticles()
      // 防止背景滚动
      document.body.style.overflow = 'hidden'
    })
  } else {
    stopParticles()
    // 恢复背景滚动
    document.body.style.overflow = ''
  }
}

// 关闭面板
const closePanel = () => {
  isPanelOpen.value = false

  // 发送面板状态变化事件
  emit('panel-state-change', false)

  stopParticles()
  // 恢复背景滚动
  document.body.style.overflow = ''
}

// 滚动到指定对话
const scrollToConversation = (index) => {
  emit('scroll-to-conversation', index)
  // 不自动关闭面板，让用户可以继续查看其他笔记
}

// 删除单个笔记
const deleteNote = (index) => {
  emit('delete-note', index)
}

// 删除所有笔记
const deleteAllNotes = () => {
  emit('delete-all-notes')
}

// 获取说话人颜色
const getSpeakerColor = (speakerId) => {
  const colors = [
    '#1890ff', '#52c41a', '#fa8c16', '#eb2f96',
    '#722ed1', '#13c2c2', '#f5222d', '#faad14'
  ]
  return colors[parseInt(speakerId) % colors.length]
}

// 格式化时间
const formatTimestamp = (milliseconds) => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 处理笔记面板滚动事件，防止滚动穿透
const handlePanelWheel = (event) => {
  // 阻止事件冒泡到父容器
  event.stopPropagation()
}

// 处理笔记列表滚动事件
const handleNotesListWheel = (event) => {
  const element = notesListRef.value
  if (!element) {
    event.preventDefault()
    event.stopPropagation()
    return
  }

  const { scrollTop, scrollHeight, clientHeight } = element
  const isAtTop = scrollTop <= 0
  const isAtBottom = scrollTop + clientHeight >= scrollHeight - 2 // 增加一点容差

  // 如果内容不需要滚动（内容高度小于容器高度）
  if (scrollHeight <= clientHeight) {
    event.preventDefault()
    event.stopPropagation()
    return
  }

  // 如果在顶部向上滚动，或在底部向下滚动，阻止事件传播
  if ((isAtTop && event.deltaY < 0) || (isAtBottom && event.deltaY > 0)) {
    event.preventDefault()
    event.stopPropagation()
  }
}

// 粒子效果
const initParticles = () => {
  if (!particlesContainer.value) return

  const container = particlesContainer.value
  const particles = []
  const particleCount = 30

  // 清除现有粒子
  container.innerHTML = ''

  // 创建粒子
  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div')
    particle.className = 'particle'

    const size = Math.random() * 6 + 2
    const colors = [
      'linear-gradient(45deg, #3b82f6, #22c55e)',
      'linear-gradient(45deg, #8b5cf6, #06b6d4)',
      'linear-gradient(45deg, #f59e0b, #ef4444)',
      'linear-gradient(45deg, #10b981, #3b82f6)'
    ]

    particle.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      background: ${colors[Math.floor(Math.random() * colors.length)]};
      border-radius: 50%;
      opacity: ${Math.random() * 0.6 + 0.2};
      left: ${Math.random() * 100}%;
      top: ${Math.random() * 100}%;
      animation: float${i % 3} ${Math.random() * 4 + 3}s ease-in-out infinite;
      animation-delay: ${Math.random() * 3}s;
      box-shadow: 0 0 ${size * 2}px rgba(59, 130, 246, 0.3);
    `
    container.appendChild(particle)
    particles.push(particle)
  }

  // 添加CSS动画
  if (!document.getElementById('particles-style')) {
    const style = document.createElement('style')
    style.id = 'particles-style'
    style.textContent = `
      @keyframes float0 {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        33% { transform: translateY(-15px) rotate(120deg) scale(1.1); }
        66% { transform: translateY(-5px) rotate(240deg) scale(0.9); }
      }
      @keyframes float1 {
        0%, 100% { transform: translateX(0px) rotate(0deg) scale(1); }
        50% { transform: translateX(20px) rotate(180deg) scale(1.2); }
      }
      @keyframes float2 {
        0%, 100% { transform: translate(0px, 0px) rotate(0deg) scale(1); }
        25% { transform: translate(10px, -10px) rotate(90deg) scale(0.8); }
        50% { transform: translate(-5px, -20px) rotate(180deg) scale(1.1); }
        75% { transform: translate(-15px, -5px) rotate(270deg) scale(0.9); }
      }
    `
    document.head.appendChild(style)
  }
}

const stopParticles = () => {
  if (particlesContainer.value) {
    particlesContainer.value.innerHTML = ''
  }
}

onUnmounted(() => {
  stopParticles()
  // 确保恢复背景滚动
  document.body.style.overflow = ''
})
</script>

<style lang="scss" scoped>
@import '../styles/notes-overview.scss';
</style>
