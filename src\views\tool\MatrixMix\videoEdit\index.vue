<template>
  <div class="vid-edit-wrapper">
    <!-- 主内容区 -->
    <div class="vid-edit-view-wrap">
      <!-- ICE WebSDK专用容器 -->
      <div 
        id="ice-websdk-container" 
        ref="websdkContainer"
        class="ice-websdk-full-container"
        data-websdk-container="true"
        data-vue-isolated="true"
        v-show="!loading"
      >
        <!-- WebSDK将在这里渲染 -->
      </div>
      
      <!-- 加载状态覆盖层 -->
      <div v-if="loading" class="websdk-loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <h3>{{ loadingTitle }}</h3>
          <p>{{ loadingMessage }}</p>
        </div>
      </div>
    </div>
    
    <!-- 素材选择抽屉 -->
    <MaterialSelector
      v-model:visible="materialSelectorVisible"
      :media-type="currentMediaType"
      :mode="editorMode"
      :editor-id="editorId"
      @select="handleMaterialSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, nextTick, markRaw, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import iceWebSDK from './utils/iceWebSDK'
import type { EditorMode } from './utils/iceNPMManager'
import MaterialSelector from './components/MaterialSelector.vue'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const loadingMessage = ref('正在初始化...')
// 使用markRaw防止Vue跟踪这个DOM元素
const websdkContainer = markRaw(ref<HTMLElement | null>(null))

// 错误处理标志
let initializationAttempted = false
let isInitializing = false

// 计算编辑器模式和ID
const editorMode = computed<EditorMode>(() => {
  // 通过路由参数判断模式
  if (route.params.TemplateId || route.query.mode === 'template') {
    return 'template';
  }
  return 'project';
})

const editorId = computed(() => {
  // 获取ID：模板ID或项目ID
  return (route.params.TemplateId || route.params.projectId || '') as string;
})

const loadingTitle = computed(() => {
  return editorMode.value === 'template' 
    ? '正在启动模板工厂编辑器...' 
    : '正在启动ICE专业视频编辑器...';
})

/**
 * 返回上一页
 */
const goBack = () => {
  router.go(-1)
}

/**
 * 安全获取容器元素
 */
function getWebSDKContainer(): HTMLElement | null {
  // 优先从ref获取
  if (websdkContainer.value) {
    return websdkContainer.value
  }
  
  // 备用方案：从DOM直接查找
  const container = document.getElementById('ice-websdk-container')
  if (container) {
    // 标记为非Vue管理的元素
    (container as any).__VUE_SKIP__ = true
    return container
  }
  
  return null
}

/**
 * 初始化ICE WebSDK编辑器 - 统一处理
 */
async function initIceWebSDK() {
  // 防止重复调用
  if (initializationAttempted || isInitializing) {
    return
  }
  
  initializationAttempted = true
  isInitializing = true
  
  try {
    loadingMessage.value = '正在检查参数...'
    
    // 等待多个渲染周期确保DOM完全就绪
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 检查容器是否存在
    const container = getWebSDKContainer()
    if (!container) {
      throw new Error('WebSDK容器未找到或未准备就绪')
    }
    
    // 清空容器内容，防止冲突
    container.innerHTML = ''
    
    // 验证ID
    if (!editorId.value) {
      ElMessage.error(`缺少${editorMode.value === 'template' ? '模板' : '项目'}ID参数`)
      goBack()
      return
    }
    
    loadingMessage.value = '正在检查WebSDK...'
    
    // 检查WebSDK是否已加载
    if (!(window as any).AliyunVideoEditor) {
      throw new Error('ICE WebSDK未加载，请检查网络连接')
    }
    
    loadingMessage.value = `正在初始化${editorMode.value === 'template' ? '模板工厂' : '云剪辑'}编辑器...`
    
    // 等待一个额外的渲染周期
    await nextTick()
    
    // 设置素材选择器回调而不是全局方法
    iceWebSDK.setMaterialSelectorCallback(showMaterialSelector);

    // 初始化ICE WebSDK - 使用统一配置
    const success = await iceWebSDK.init({
      container,
      id: editorId.value,
      mode: editorMode.value
    })
    
    if (success) {
      // 延迟显示成功消息，避免与DOM操作冲突
      setTimeout(() => {
        const successMsg = editorMode.value === 'template' 
          ? '模板工厂编辑器已就绪！可以开始编辑模板了'
          : '云剪辑编辑器已就绪！'
        ElMessage.success(successMsg)
      }, 500)
    } else {
      throw new Error(`${editorMode.value === 'template' ? '模板工厂' : '云剪辑'}编辑器初始化失败`)
    }
    
  } catch (error: any) {
    // 重置标志，允许重试
    initializationAttempted = false
    // 延迟显示错误消息，避免与DOM操作冲突
    setTimeout(() => {
      ElMessage.error('初始化失败: ' + error.message)
    }, 100)
  } finally {
    isInitializing = false
    // 延迟隐藏加载状态
    setTimeout(() => {
      loading.value = false
    }, 300)
  }
}

/**
 * 手动保存（仅模板工厂模式）
 */
const saveTemplate = async () => {
  try {
    if (!iceWebSDK.isInitialized()) {
      ElMessage.warning('编辑器未初始化，无法保存');
      return;
    }
    
    if (editorMode.value !== 'template') {
      ElMessage.info('云剪辑项目会自动保存');
      return;
    }
    
    // 模板工厂手动保存逻辑
    ElMessage.info('正在保存模板...');
    // 实际保存逻辑由WebSDK内部处理
    
  } catch (error: any) {
    ElMessage.error(`保存时发生异常: ${error.message}`);
  }
}

/**
 * 键盘快捷键保存
 */
const handleKeyboardSave = (event: KeyboardEvent) => {
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault();
    if (editorMode.value === 'template') {
      saveTemplate();
    }
  }
}

// 素材选择器状态
const materialSelectorVisible = ref(false)
const currentMediaType = ref<'video' | 'audio' | 'image'>('video')
const materialSelectResolve = ref<((materials: any[]) => void) | null>(null)

/**
 * 显示素材选择器
 */
const showMaterialSelector = (mediaType: 'video' | 'audio' | 'image'): Promise<any[]> => {
  return new Promise((resolve) => {
    currentMediaType.value = mediaType
    materialSelectResolve.value = resolve
    materialSelectorVisible.value = true
  })
}

/**
 * 处理素材选择
 */
const handleMaterialSelect = (materials: any[]) => {
  if (materialSelectResolve.value) {
    materialSelectResolve.value(materials)
    materialSelectResolve.value = null
  }
  materialSelectorVisible.value = false
}

onMounted(() => {
  // 确保只执行一次
  if (!initializationAttempted && !isInitializing) {
    // 使用requestAnimationFrame确保在下一个重绘周期执行
    requestAnimationFrame(() => {
      setTimeout(() => {
        initIceWebSDK()
      }, 100)
    })
  }
  
  // 添加键盘快捷键监听
  document.addEventListener('keydown', handleKeyboardSave);
})

onBeforeUnmount(() => {
  try {
    // 先设置加载状态，防止DOM操作
    loading.value = true
    
    // 异步清理，避免阻塞卸载过程
    setTimeout(() => {
      try {
        iceWebSDK.destroy()
      } catch (error) {
        console.error('❌ ICE WebSDK清理失败:', error)
      }
    }, 0)
    
  } catch (error) {
    console.error('❌ ICE WebSDK清理失败:', error)
  }
  
  // 移除键盘快捷键监听
  document.removeEventListener('keydown', handleKeyboardSave);
})

// 暴露保存方法供外部调用（仅模板工厂模式）
defineExpose({
  saveTemplate
});
</script>

<style lang="scss" scoped>
.vid-edit-wrapper {
  background-color: #1a1e22;
  color: #e0e0e0;
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  
  /* 优化事件处理，防止passive事件警告 */
  touch-action: manipulation;
  -webkit-overflow-scrolling: touch;
}

.vid-edit-view-wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  overflow: hidden;
  min-height: 0;
  width: 100%;
}

.ice-websdk-full-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  
  /* 严格的DOM隔离 */
  contain: layout style paint size;
  isolation: isolate;
  transform: translateZ(0); /* 创建新的层叠上下文 */
  
  /* 防止Vue响应式干扰 */
  pointer-events: auto;
  -webkit-user-select: auto;
  user-select: auto;
  
  /* 优化事件处理性能 */
  touch-action: auto;
  -webkit-overflow-scrolling: touch;
  will-change: auto;
  
  /* 重置可能的样式冲突 */
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  box-sizing: border-box;
}

.websdk-loading-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1e22 0%, #24292e 100%);
  z-index: 1000;
  
  .loading-content {
    text-align: center;
    color: #e0e0e0;
    
    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 4px solid #363636;
      border-top: 4px solid #409eff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px auto;
    }
    
    h3 {
      font-size: 24px;
      margin: 20px 0 15px 0;
      color: #ffffff;
      font-weight: 600;
    }
    
    p {
      font-size: 14px;
      margin: 8px 0;
      color: #a8b2c2;
      line-height: 1.5;
    }
  }
}

/* 全局样式：防止Vue的transition和animation效果影响WebSDK */
:global([data-websdk-container="true"]) {
  /* 完全重置样式 */
  all: unset !important;
  display: block !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: #1e1e1e !important;
  
  /* 防止动画和过渡 */
  transition: none !important;
  animation: none !important;
  transform: none !important;
  
  /* 优化事件处理 */
  touch-action: auto !important;
  -webkit-touch-callout: auto !important;
  -webkit-user-select: auto !important;
  user-select: auto !important;
  pointer-events: auto !important;
  
  /* 防止层叠上下文问题 */
  contain: layout style paint size !important;
  isolation: isolate !important;
}

:global([data-websdk-container="true"] *) {
  /* 让WebSDK完全控制内部元素 */
  touch-action: auto !important;
  -webkit-touch-callout: auto !important;
  -webkit-user-select: auto !important;
  user-select: auto !important;
  pointer-events: auto !important;
}

/* Vue隔离标记的容器 */
:global([data-vue-isolated="true"]) {
  /* 防止Vue响应式系统处理这些元素 */
  pointer-events: auto !important;
  -webkit-user-select: auto !important;
  user-select: auto !important;
  
  /* 事件优化 */
  touch-action: auto !important;
}

/* 覆盖可能的全局样式 */
:global(#ice-websdk-container) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1 !important;
  background-color: #1e1e1e !important;
  overflow: hidden !important;
}

/* 防止Element Plus等UI库的样式干扰 */
:global(.el-loading-mask) {
  z-index: 999 !important;
}

:global(.el-message) {
  z-index: 9999 !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .websdk-loading-overlay {
    .loading-content {
      h3 {
        font-size: 20px;
      }
      
      p {
        font-size: 13px;
      }
    }
  }
}
</style>