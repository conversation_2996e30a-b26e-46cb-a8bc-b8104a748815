/**
 * @file commonUtils.ts
 * @description 通用工具函数集合
 *              整合各个工具文件中的重复功能，提供统一的工具方法
 */

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时长（HH:MM:SS格式）
 * @param seconds 秒数
 * @returns 格式化后的时长字符串 (HH:MM:SS)
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * 根据文件名推断媒体类型
 * @param fileName 文件名
 * @param activeTab 当前活动标签页（可选，用于区分音频和音乐）
 * @returns 媒体类型：video、audio、music、image、font、text
 */
export function inferMediaType(fileName: string, activeTab?: string): 'video' | 'audio' | 'music' | 'image' | 'font' | 'text' {
  const name = fileName.toLowerCase();
  
  // 视频文件
  if (name.match(/\.(mp4|avi|mkv|mov|wmv|flv|webm|m4v)$/)) {
    return 'video';
  }

  // 音频文件
  if (name.match(/\.(mp3|wav|flac|aac|m4a|wma|ogg)$/)) {
    // 根据当前标签页判断是音频还是音乐
    return activeTab === 'music' ? 'music' : 'audio';
  }

  // 图片文件
  if (name.match(/\.(jpg|jpeg|png|gif|bmp|webp|svg)$/)) {
    return 'image';
  }

  // 字体文件
  if (name.match(/\.(ttf|otf|woff|woff2|eot)$/)) {
    return 'font';
  }

  // 文本文件
  if (name.match(/\.(txt|srt|ass|vtt|sub)$/)) {
    return 'text';
  }
  
  // 如果无法识别，使用当前标签页作为默认值
  return (activeTab as any) || 'video';
}

/**
 * 验证媒资ID格式
 * @param mediaId 媒资ID
 * @returns 是否有效
 */
export function validateMediaId(mediaId: string): boolean {
  // 媒资ID通常为32位字符串，包含字母和数字
  const mediaIdPattern = /^[a-zA-Z0-9]{32}$/;
  return mediaIdPattern.test(mediaId);
}

/**
 * 安全地解析JSON字符串，带错误处理
 * @param jsonString JSON字符串或对象
 * @param context 上下文信息，用于错误提示
 * @returns 解析后的对象，解析失败时返回null
 */
export function safeJsonParse(jsonString: string | object, context: string = 'JSON'): any {
  if (typeof jsonString === 'object') {
    return jsonString;
  }
  
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error(`解析${context}失败:`, error);
    return null;
  }
}

/**
 * 数组去重
 * @param arr 数组
 * @returns 去重后的数组
 */
export function uniqueArray<T>(arr: T[]): T[] {
  return [...new Set(arr)];
}

/**
 * 过滤数组中的空值
 * @param arr 数组
 * @returns 过滤后的数组
 */
export function filterEmptyValues<T>(arr: T[]): T[] {
  return arr.filter(item => item !== null && item !== undefined && item !== '');
}

/**
 * 延迟执行
 * @param ms 延迟毫秒数
 * @returns Promise
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: number;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
} 