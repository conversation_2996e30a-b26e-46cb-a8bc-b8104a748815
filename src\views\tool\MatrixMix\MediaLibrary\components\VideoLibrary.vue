<template>
  <div class="video-library">
    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" @click="handleUpload">
          <el-icon><Plus /></el-icon>
          上传视频
        </el-button>
        <el-button type="danger" @click="handleDelete" :disabled="selectedItems.length === 0">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>

      <div class="right-actions">
        <!-- 筛选业务类型 -->
        <el-select v-model="businessType" placeholder="业务类型" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 筛选来源 -->
        <el-select v-model="source" placeholder="来源" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 筛选资源状态 -->
        <el-select v-model="status" placeholder="资源状态" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 日期筛选 -->
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px; margin-right: 10px;"
          @change="handleDateChange"
        />
        <!-- 排序 -->
        <el-select v-model="sortBy" placeholder="排序" style="width: 120px;" >
          <el-option label="升序" value="asc" />
          <el-option label="降序" value="desc" />
        </el-select>
        <el-button type="primary" @click="handleFilter">筛选</el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 视频列表 -->
      <div class="video-panel">
        <div v-if="loading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>

        <div v-else-if="(props.mediaList || []).length === 0" class="empty-state">
          <el-empty description="暂无数据">
            <el-button type="primary" @click="handleUpload">上传视频</el-button>
          </el-empty>
        </div>

        <div v-else class="video-grid">
          <div v-for="video in (props.mediaList || [])"
               :key="video.MediaId"
               :class="['video-item', { selected: selectedItems.includes(video.MediaId) }]"
               @click="toggleSelect(video.MediaId)">
            <div class="video-thumbnail">
              <img :src="getPreviewImage(video)" :alt="video.MediaBasicInfo.Title" />
              <div class="video-overlay">
                <el-icon class="play-icon"><VideoPlay /></el-icon>
              </div>
              <!-- 展示时长（如有） -->
              <div v-if="video.FileInfoList?.[0]?.FileBasicInfo?.Duration" class="duration">
                {{ formatDuration(parseFloat(video.FileInfoList[0].FileBasicInfo.Duration) || 0) }}
              </div>
            </div>
            <div class="video-content">
              <div class="video-info">
                <h4 class="video-title">{{ video.MediaBasicInfo.Title }}</h4>
                <div class="video-meta-row">
                  <span>来源：{{ SourceMap[video.MediaBasicInfo.Source] || video.MediaBasicInfo.Source }}</span>
                  <span>业务类型：{{ BusinessTypeMap[video.MediaBasicInfo.BusinessType] || video.MediaBasicInfo.BusinessType }}</span>
                </div>
                <div class="video-meta-row">
                  <span>状态：{{ StatusMap[video.MediaBasicInfo.Status] || video.MediaBasicInfo.Status }}</span>
                  <span>创建：{{ formatDate(video.MediaBasicInfo.CreateTime) }}</span>
                </div>
                <p v-if="video.MediaBasicInfo.Description" class="video-description">
                  {{ video.MediaBasicInfo.Description }}
                </p>
              </div>
              <div class="video-actions">
                <el-button size="small" type="primary" @click.stop="handlePreview(video)">预览</el-button>
                <el-button size="small" type="danger" @click.stop="handleDeleteSingle(video.MediaId)">删除</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传组件 -->
    <MediaUploader
      v-model:visible="uploadDialogVisible"
      media-type="video"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus,Delete,Refresh,Loading,VideoPlay} from '@element-plus/icons-vue'
import type { MediaListQueryParams, MediaInfo } from '../../types/media'
import { formatDuration } from '../../utils/commonUtils'
import { BusinessTypeMap, StatusMap, SourceMap, mapToOptions } from '../../types/media'
import MediaUploader from './add/MediaUploader.vue'

// 获取视频预览图（优先级：CoverURL > Snapshots > SpriteImages > 占位图）
function getPreviewImage(video: MediaInfo): string {
  const info = video.MediaBasicInfo
  // 1. CoverURL
  if (info.CoverURL) return info.CoverURL
  // 2. Snapshots（可能为字符串或数组）
  if (info.Snapshots) {
    try {
      if (Array.isArray(info.Snapshots)) {
        if (info.Snapshots.length > 0) return info.Snapshots[0]
      } else if (typeof info.Snapshots === 'string') {
        // 可能是逗号分隔或JSON数组
        if (info.Snapshots.startsWith('[')) {
          const arr = JSON.parse(info.Snapshots)
          if (Array.isArray(arr) && arr.length > 0) return arr[0]
        } else if (info.Snapshots.includes(',')) {
          return info.Snapshots.split(',')[0]
        } else {
          return info.Snapshots
        }
      }
    } catch {}
  }
  // 3. SpriteImages（通常为图片地址或JSON）
  if (info.SpriteImages) {
    try {
      if (typeof info.SpriteImages === 'string') {
        if (info.SpriteImages.startsWith('[')) {
          const arr = JSON.parse(info.SpriteImages)
          if (Array.isArray(arr) && arr.length > 0) return arr[0]
        } else if (info.SpriteImages.includes(',')) {
          return info.SpriteImages.split(',')[0]
        } else {
          return info.SpriteImages
        }
      }
    } catch {}
  }
  // 4. 占位图
  return '/placeholder-video.jpg'
}

// 定义组件的事件和props
const emit = defineEmits<{
    refreshMediaList: [page?: number]
}>()

const callParentRefreshMediaList = (page = 1) => {
  emit('refreshMediaList', page)
}

// 定义props
const props = defineProps<{
  mediaList?: MediaInfo[],
  currentPage?: number,
  pageSize?: number,
  total?: number
}>()

// 响应式数据
const loading = ref(false)
const selectedItems = ref<string[]>([])
const uploadDialogVisible = ref(false)

// 筛选相关变量
const businessType = ref('')
const source = ref('')
const status = ref('')
const dateRange = ref<[Date, Date] | null>(null)
const sortBy = ref<string>('desc')
const startTime = ref<string>('')
const endTime = ref<string>('')

// 下拉选项
const businessTypeOptions = mapToOptions(BusinessTypeMap)
const sourceOptions = mapToOptions(SourceMap)
const statusOptions = mapToOptions(StatusMap)



// 日期筛选处理
const handleDateChange = (val: [Date, Date] | null) => {
  if (val && val.length === 2) {
    startTime.value = val[0].toISOString()
    endTime.value = val[1].toISOString()
  } else {
    startTime.value = ''
    endTime.value = ''
  }
}

// 筛选按钮处理
const handleFilter = () => {
  callParentRefreshMediaList(1)
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 方法
const handleUpload = () => {
  uploadDialogVisible.value = true
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除选中的视频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // 执行删除操作
    ElMessage.success('删除成功')
    selectedItems.value = []
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleRefresh = () => {
  callParentRefreshMediaList()
}

const toggleSelect = (videoId: string) => {
  const index = selectedItems.value.indexOf(videoId)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(videoId)
  }
}

const handlePreview = (video: any) => {
  ElMessage.info(`预览视频: ${video.MediaBasicInfo.Title}`)
}

const handleDeleteSingle = async (videoId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个视频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
  } catch {
    ElMessage.info('已取消删除')
  }
}

// 上传成功处理
const handleUploadSuccess = (result: any) => {
  ElMessage.success('视频上传成功！')
  uploadDialogVisible.value = false
  callParentRefreshMediaList()
}

// 上传错误处理
const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
  ElMessage.error(`上传失败: ${error.message || '未知错误'}`)
}

// 初始化
onMounted(() => {
  callParentRefreshMediaList()
})
</script>

<style lang="scss" scoped>
.video-library {
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 18px 22px 18px 22px;
  background: #f7fafd;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  gap: 18px;
  .left-actions {
    display: flex;
    gap: 14px;
    .el-button {
      border-radius: 7px;
      font-size: 15px;
      font-weight: 500;
      padding: 0 18px;
      height: 38px;
      &.el-button--primary {
        background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
        color: #fff;
        border: none;
        box-shadow: 0 2px 8px rgba(64,158,255,0.08);
      }
      &.el-button--danger {
        background: #fff0f0;
        color: #f56c6c;
        border: 1px solid #fbc4c4;
      }
      &:disabled {
        background: #f5f7fa !important;
        color: #bfbfbf !important;
        border: 1px solid #e4e7ed !important;
      }
      .el-icon {
        margin-right: 6px;
      }
    }
  }
  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    .el-select, .el-date-picker {
      border-radius: 7px;
      font-size: 15px;
      height: 38px;
      .el-input__wrapper, .el-input__inner {
        border-radius: 7px;
        height: 38px;
        font-size: 15px;
      }
    }
    .el-button {
      border-radius: 7px;
      font-size: 15px;
      padding: 0 18px;
      height: 38px;
      &.el-button--primary {
        background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
        color: #fff;
        border: none;
      }
    }
  }
}

.content-wrapper {
  display: flex;
  gap: 20px;
  min-height: 0;
}

.video-panel {
  width: 100%;
  height: 555px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  padding: 10px 18px 18px 18px;
  display: flex;
  flex-direction: column;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 5px;
    color: #666;
    .el-icon {
      font-size: 32px;
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
  }

  .video-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    overflow-y: auto;
    align-items: flex-start;
  }
  .video-item {
    flex: 0 0 220px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    height: 260px;
    border: 2px solid transparent;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(.4,0,.2,1);
    background: #f8fafd;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    &:hover {
      border-color: #409eff;
      transform: translateY(-3px) scale(1.02);
      box-shadow: 0 6px 18px rgba(64,158,255,0.10);
    }
    &.selected {
      border-color: #409eff;
      background: #ecf5ff;
    }
    .video-thumbnail {
      height: 110px;
      min-height: 110px;
      max-height: 110px;
      position: relative;
      width: 100%;
      overflow: hidden;
      border-radius: 10px 10px 0 0;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 10px 10px 0 0;
      }
      .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.22);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;
        .play-icon {
          font-size: 34px;
          color: white;
        }
      }
      &:hover .video-overlay {
        opacity: 1;
      }
      .duration {
        position: absolute;
        bottom: 8px;
        right: 8px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 2px 8px;
        border-radius: 5px;
        font-size: 13px;
      }
    }
    .video-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
      padding: 18px 10px 16px 10px;
      min-height: 0;
    }
    .video-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      min-height: 0;
      .video-title {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .video-meta-row {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #888;
        margin-bottom: 2px;
        gap: 6px;
        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
        }
      }
      .video-description {
        font-size: 12px;
        color: #aaa;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .video-actions {
      margin-top: 6px;
      padding: 0 0 4px 0;
      border-top: 1px solid #f0f0f0;
      display: flex;
      gap: 10px;
      .el-button {
        flex: 1;
        border-radius: 7px;
        font-size: 14px;
      }
    }
  }
}
</style>
