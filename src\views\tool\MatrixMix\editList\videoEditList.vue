<template>
  <div class="app-container">
    <!-- 顶部标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <div class="title-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
              <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" fill="currentColor"/>
            </svg>
          </div>
          <div class="title-text">
            <h1>云剪辑工作台</h1>
            <p>智能视频编辑，让创作更简单</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="large" icon="Plus" @click="handleCreate" class="create-btn">
            创建新工程
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主内容卡片 -->
    <el-card class="main-card">
      <!-- 工具栏 -->
      <div class="toolbar-section">
        <div class="search-filters">
          <div class="search-group">
            <el-input v-model="queryParams.keyword" placeholder="搜索工程标题或描述..." class="search-input" clearable @keyup.enter="handleQuery">
              <template #prefix>
                <el-icon class="search-icon"><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="queryParams.status" placeholder="筛选状态" class="status-select" clearable @change="handleQuery" style="width: 260px;">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" icon="Search" @click="handleQuery" class="search-btn">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery" class="refresh-btn">重置</el-button>
          </div>
          <div class="action-buttons">
            <el-button :disabled="multiple" icon="Delete" type="danger" @click="handleDelete()" class="delete-btn">
              批量删除
            </el-button>
            <ExportTaskManager ref="exportTaskManagerRef" />
          </div>
        </div>
      </div>

      <!-- 项目列表 -->
      <div class="project-list-container">
        <el-table v-loading="loading" :data="projectList" class="project-table"
          @selection-change="handleSelectionChange"
          :header-cell-style="{ background: '#fafbfc', color: '#303133', fontWeight: '600' }">
          <el-table-column type="selection" width="50" align="center"></el-table-column>

          <el-table-column label="预览" width="140" align="center">
            <template #default="{ row }">
              <div class="project-preview">
                <el-image class="project-thumbnail" :src="row.CoverURL" fit="cover">
                  <template #error>
                    <div class="thumbnail-placeholder">
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                        <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" fill="#c0c4cc"/>
                      </svg>
                    </div>
                  </template>
                </el-image>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="项目信息" min-width="280">
            <template #default="{ row }">
              <div class="project-details">
                <div class="project-title-main">{{ row.Title }}</div>
                <div class="project-id-text" :title="row.ProjectId">ID: {{ row.ProjectId }}</div>
                <div v-if="row.Description" class="project-description">{{ row.Description }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="150" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusConfig(row.Status).type" :effect="getStatusConfig(row.Status).effect" class="status-tag" round>
                {{ getStatusConfig(row.Status).label }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="220" align="center">
            <template #default="scope">
              <div class="time-display">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span class="time-text">{{ parseTime(scope.row.CreateTime, '{y}-{m}-{d} {h}:{i}') }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="更新时间" width="220" align="center">
            <template #default="scope">
              <div class="time-display">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span class="time-text">{{ parseTime(scope.row.ModifiedTime, '{y}-{m}-{d} {h}:{i}') }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="270" align="center" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="primary" size="small" icon="Edit" @click="handleEdit(row)" class="edit-btn">编辑</el-button>
                <el-button type="success" size="small" icon="Download" @click="handleExport(row)" class="export-btn">导出</el-button>
                <el-button type="danger" size="small" icon="Delete" @click="handleDelete(row)" class="delete-btn">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          class="custom-pagination"
        />
      </div>
    </el-card>

    <!-- 创建工程对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible"width="520px" class="create-dialog"
      :close-on-click-modal="false" destroy-on-close>
      <div class="dialog-content">
        <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="100px" class="project-form">
          <el-form-item label="工程标题" prop="Title">
            <el-input v-model="form.Title" placeholder="请输入工程标题" size="large" maxlength="50" show-word-limit />
          </el-form-item>
          <el-form-item label="工程描述" prop="Description">
            <el-input v-model="form.Description" type="textarea" placeholder="请输入工程描述（可选）" :rows="4" maxlength="200" show-word-limit />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-actions">
          <el-button size="large" @click="cancel" class="cancel-btn">取消</el-button>
          <el-button type="primary" size="large" @click="submitForm" class="submit-btn">创建工程</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导出对话框 -->
    <ExportDialog v-model:visible="state.export.dialogVisible" :project-info="state.export.currentProject"
      @success="handleExportSuccess"/>
  </div>
</template>

<script setup lang="ts" name="VideoEditList">
import { ref, reactive, onMounted, onActivated, toRefs } from 'vue';
import { useRouter } from 'vue-router';
import { listEditingProjects, deleteEditingProjects, createEditingProject } from '../api/videoEdit';
import type { ProjectInfo } from '../api/videoEdit';
import type { VideoEditListParams, CreateEditingProjectDTO } from '../types/videoEdit';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';
import { Search, Refresh, Edit, Download } from '@element-plus/icons-vue';
import { parseTime } from '@/utils/ruoyi';
import ExportDialog from './components/ExportDialog.vue';
import ExportTaskManager from './components/ExportTaskManager.vue';

defineOptions({
  name: "VideoEditList"
});

const router = useRouter();
const activeTab = ref('video');
const projectFormRef = ref<FormInstance>();
const exportTaskManagerRef = ref();


// 状态配置 - 修复值映射问题
const statusConfig = {
  base: {
    Draft: { label: '草稿', type: 'info', effect: 'light' },
    Editing: { label: '编辑中', type: 'primary', effect: 'light' },
    Producing: { label: '制作中', type: 'warning', effect: 'light' },
    Produced: { label: '已制作完成', type: 'success', effect: 'light' },
    ProduceFailed: { label: '制作失败', type: 'danger', effect: 'light' },
    Normal: { label: '正常', type: 'success', effect: 'light' }
  },
  // 生成选项列表 - 使用英文值作为value
  get options() {
    return [
      { label: '全部', value: undefined },
      ...Object.entries(this.base).map(([value, item]) => ({ label: item.label, value }))
    ];
  },
  // 生成映射表
  get mapping() {
    return {
      ...this.base,
      default: { label: '未知', type: 'info', effect: 'light' }
    };
  }
};

// 导出状态选项供模板使用
const statusOptions = statusConfig.options;

// 获取状态配置
function getStatusConfig(status: string) {
  return statusConfig.mapping[status as keyof typeof statusConfig.mapping] || statusConfig.mapping.default;
}

const state = reactive({
  loading: true,
  ids: [] as string[],
  multiple: true,
  total: 0,
  projectList: [] as ProjectInfo[],
  queryParams: {
    pageNum: 1,
    pageSize: 5,
    keyword: '',
    status: undefined,
  } as VideoEditListParams,
  form: {} as CreateEditingProjectDTO,
  dialog: {
    visible: false,
    title: ''
  },
  // 导出相关状态
  export: {
    dialogVisible: false,
    currentProject: null as ProjectInfo | null
  },
  rules: {
    Title: [
      { required: true, message: "工程标题不能为空", trigger: "blur" }
    ],
  } as FormRules,
});

const { queryParams, loading, projectList, total, multiple, form, dialog, rules } = toRefs(state);



/** 取消按钮 */
function cancel() {
  state.dialog.visible = false;
  resetForm();
}

/** 表单重置 */
function resetForm() {
  state.form = {
    Title: '',
    Description: ''
  };
  projectFormRef.value?.resetFields();
}

/** 新增按钮操作 */
function handleCreate() {
  resetForm();
  state.dialog.visible = true;
  state.dialog.title = "创建剪辑工程";
}

/** 编辑按钮操作 */
function handleEdit(row: ProjectInfo) {
  router.push({
    path: `/tool/matrix-mix/video-edit/${row.ProjectId}`,
    query: { mode: 'project' }
  });
}

/** 提交按钮 */
async function submitForm() {
  projectFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        // 转换为正确的API参数格式
        const createParams = {
          Title: state.form.Title,
          Description: state.form.Description,
          Timeline: state.form.Timeline ? JSON.stringify(state.form.Timeline) : undefined
        };
        await createEditingProject(createParams);
        ElMessage.success('创建成功');
        state.dialog.visible = false;
        getList();
      } catch (error) {
        console.error("创建失败: ", error);
        ElMessage.error("创建失败");
      }
    }
  });
}

/** 查询剪辑工程列表 */
async function getList() {
  state.loading = true;
  try {
    // 构建正确的API参数
    const apiParams = {
      keyword: state.queryParams.keyword,
      status: state.queryParams.status,
      maxResults: 100, // 获取更多数据用于前端分页和搜索
    };
    const response = await listEditingProjects(apiParams);
    const allProjects = response.data.ProjectList || [];
    state.total = allProjects.length; // 使用实际数组长度作为总数
    const { pageNum, pageSize } = state.queryParams;
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    state.projectList = allProjects.slice(start, end);
  } catch (error) {
    ElMessage.error('获取剪辑工程列表失败');
    console.error('API调用错误:', error);
  } finally {
    state.loading = false;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  state.queryParams.pageNum = 1;
  state.queryParams.keyword = '';
  state.queryParams.status = undefined; // Reset status filter
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: ProjectInfo[]) {
  state.ids = selection.map(item => item.ProjectId);
  state.multiple = !selection.length;
}

/** 删除按钮操作 */
async function handleDelete(row?: ProjectInfo) {
  const projectIds = row ? [row.ProjectId] : state.ids;
  if (projectIds.length === 0) {
    ElMessage.warning('请选择要删除的工程');
    return;
  }
  try {
    await ElMessageBox.confirm(
      `是否确认删除工程ID为 "${projectIds.join(', ')}" 的数据项？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    await deleteEditingProjects(projectIds);
    ElMessage.success('删除成功');
    getList(); // Refresh list
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
}

/** 导出按钮操作 */
function handleExport(row: ProjectInfo) {
  state.export.currentProject = row;
  state.export.dialogVisible = true;
}

/** 导出成功回调 */
function handleExportSuccess(jobId: string) {
  // 添加到导出任务管理器
  if (exportTaskManagerRef.value && state.export.currentProject) {
    const exportTask = {
      jobId: jobId,
      projectId: state.export.currentProject.ProjectId,
      projectTitle: state.export.currentProject.Title,
      exportType: 'BaseTimeline', 
      bucket: 'szb-pc', 
      status: 'processing' as const,
      exportTime: Date.now()
    };
    exportTaskManagerRef.value.addExportTask(exportTask);
  }
}

onMounted(() => {
  getList();
});

// 修复路由切换后数据不显示的问题
onActivated(() => {
  // 当组件被激活时重新加载数据
  getList();
});

</script>

<style lang="scss" scoped>
.app-container {
  padding: 0;
  height: 700px;
  // 页面头部
  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 32px 40px;
    border-radius: 20px;
    color: white;
    margin-bottom: 20px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .title-section {
      display: flex;
      align-items: center;
      gap: 16px;

      .title-icon {
        width: 56px;
        height: 56px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        backdrop-filter: blur(10px);
      }

      .title-text {
        h1 {
          margin: 0;
          font-size: 32px;
          font-weight: 700;
          line-height: 1.2;
        }

        p {
          margin: 4px 0 0 0;
          font-size: 16px;
          opacity: 0.9;
          font-weight: 400;
        }
      }
    }

    .header-actions {
      .create-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 12px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 主卡片
  .main-card {
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
    background: white;

    :deep(.el-card__body) {
      padding: 0;
    }
  }

  // 工具栏区域
  .toolbar-section {
    padding: 32px 40px;
    background: #fafbfc;
    border-bottom: 1px solid #e1e8ed;

    .search-filters {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 24px;
      flex-wrap: wrap;

      .search-group {
        display: flex;
        align-items: center;
        gap: 16px;
        flex: 1;

        .search-input {
          max-width: 280px;
          margin-right: 12px;

          :deep(.el-input__wrapper) {
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e1e8ed;
            transition: all 0.3s ease;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
            }

            &.is-focus {
              border-color: #409eff;
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            }
          }

          .search-icon {
            color: #8c939d;
          }
        }

        .search-btn {
          height: 32px;
          border-radius: 12px;
          font-weight: 500;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transform: translateY(-1px);
          }
        }

        .status-select {
          min-width: 140px;

          :deep(.el-input__wrapper) {
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e1e8ed;
            transition: all 0.3s ease;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;

        .refresh-btn {
          background: white;
          border: 1px solid #e1e8ed;
          color: #606266;
          border-radius: 10px;
          padding: 10px 20px;
          transition: all 0.3s ease;

          &:hover {
            background: #f5f7fa;
            border-color: #c0c4cc;
            transform: translateY(-1px);
          }
        }

        .delete-btn {
          background: linear-gradient(135deg, #ff6b6b, #ee5a52);
          border: none;
          color: white;
          border-radius: 10px;
          padding: 10px 20px;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            background: linear-gradient(135deg, #ff5252, #e53935);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
          }

          &:disabled {
            background: #f5f7fa;
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }
      }
    }
  }

  // 项目列表容器
  .project-list-container {
    padding: 0;

    .project-table {
      border: none;

      :deep(.el-table__header) {
        th {
          background: #fafbfc;
          border: none;
          color: #303133;
          font-weight: 600;
          font-size: 14px;
          padding: 20px 16px;

          .cell {
            color: #606266;
          }
        }
      }

      :deep(.el-table__body) {
        tr {
          border: none;
          transition: all 0.3s ease;

          &:hover {
            background: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
          }

          td {
            border: none;
            padding: 20px 16px;
            vertical-align: middle;

            &:first-child {
              border-left: 3px solid transparent;
            }
          }

          &:hover td:first-child {
            border-left-color: #409eff;
          }
        }
      }
    }
  }

  // 项目预览
  .project-preview {
    display: flex;
    justify-content: center;

    .project-thumbnail {
      width: 120px;
      height: 68px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border: 2px solid #f0f2f5;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .thumbnail-placeholder {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #8c939d;
      }
    }
  }

  // 项目详情
  .project-details {
    .project-title-main {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 6px;
      line-height: 1.4;
    }

    .project-id-text {
      font-size: 12px;
      color: #909399;
      background: #f0f2f5;
      padding: 2px 8px;
      border-radius: 6px;
      display: inline-block;
      margin-bottom: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
    }

    .project-description {
      font-size: 13px;
      color: #606266;
      line-height: 1.4;
      max-width: 250px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  // 状态标签
  .status-tag {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    border: none;

    &.el-tag--success {
      background: linear-gradient(135deg, #67c23a, #85ce61);
      color: white;
    }

    &.el-tag--warning {
      background: linear-gradient(135deg, #e6a23c, #f0a020);
      color: white;
    }

    &.el-tag--danger {
      background: linear-gradient(135deg, #f56c6c, #f78989);
      color: white;
    }

    &.el-tag--info {
      background: linear-gradient(135deg, #909399, #b1b3b8);
      color: white;
    }

    &.el-tag--primary {
      background: linear-gradient(135deg, #409eff, #66b1ff);
      color: white;
    }
  }

  // 时间显示
  .time-display {
    display: flex;
    align-items: center;
    gap: 6px;

    .time-icon {
      color: #c0c4cc;
      font-size: 14px;
    }

    .time-text {
      font-size: 13px;
      color: #606266;
      font-family: 'Monaco', 'Menlo', monospace;
    }
  }

  // 操作按钮
  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .edit-btn, .export-btn, .delete-btn {
      border-radius: 8px;
      font-size: 12px;
      padding: 6px 12px;
      font-weight: 500;
      transition: all 0.3s ease;
      border: none;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }

    .edit-btn {
      background: linear-gradient(135deg, #409eff, #66b1ff);

      &:hover {
        background: linear-gradient(135deg, #337ecc, #5dade2);
      }
    }

    .export-btn {
      background: linear-gradient(135deg, #67c23a, #85ce61);

      &:hover {
        background: linear-gradient(135deg, #529b2e, #6cb52d);
      }
    }

    .delete-btn {
      background: linear-gradient(135deg, #f56c6c, #f78989);

      &:hover {
        background: linear-gradient(135deg, #f24c4c, #e85656);
      }
    }
  }

  // 分页样式
  .pagination-wrapper {
    background-color: #ffffff;
    .custom-pagination {
       background-color: #ffffff;
      :deep(.el-pagination) {
        justify-content: center;

        .el-pager li {
          border-radius: 8px;
          margin: 0 4px;
          min-width: 36px;
          height: 36px;
          line-height: 36px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            background: #409eff;
            color: white;
            transform: translateY(-1px);
          }

          &.is-active {
            background: linear-gradient(135deg, #409eff, #66b1ff);
            color: white;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }
        }

        .btn-prev, .btn-next {
          border-radius: 8px;
          margin: 0 8px;
          width: 36px;
          height: 36px;
          transition: all 0.3s ease;

          &:hover {
            background: #409eff;
            color: white;
            transform: translateY(-1px);
          }
        }
      }
    }
  }

  // 创建对话框样式
  .create-dialog {
    :deep(.el-dialog) {
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);

      .el-dialog__header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 24px 32px;
        margin: 0;

        .el-dialog__title {
          font-size: 20px;
          font-weight: 600;
        }

        .el-dialog__headerbtn {
          top: 24px;
          right: 32px;

          .el-dialog__close {
            color: white;
            font-size: 20px;

            &:hover {
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }
      }

      .el-dialog__body {
        padding: 0;
      }
    }

    .dialog-content {
      padding: 32px;

      .project-form {
        .el-form-item {
          margin-bottom: 24px;

          .el-form-item__label {
            font-weight: 600;
            color: #303133;
          }

          .el-input, .el-textarea {
            :deep(.el-input__wrapper), :deep(.el-textarea__inner) {
              border-radius: 12px;
              border: 2px solid #e1e8ed;
              transition: all 0.3s ease;

              &:hover {
                border-color: #409eff;
              }

              &.is-focus {
                border-color: #409eff;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
              }
            }
          }
        }
      }
    }

    .dialog-actions {
      padding: 0px 32px;
      display: flex;
      justify-content: flex-end;
      gap: 16px;

      .cancel-btn {
        background: white;
        border: 2px solid #e1e8ed;
        color: #606266;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          background: #f5f7fa;
          border-color: #c0c4cc;
        }
      }

      .submit-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, #5a6fd8, #6a4190);
          transform: translateY(-1px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .app-container {
    .page-header {
      padding: 24px 32px;

      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }
    }

    .main-card {
      margin: -20px 32px 32px 32px;
    }

    .toolbar-section {
      padding: 24px 32px;

      .search-filters {
        flex-direction: column;
        gap: 16px;

        .search-group {
          justify-content: center;
        }
      }
    }

    .pagination-wrapper {
      padding: 20px 32px;
    }
  }
}

@media (max-width: 992px) {
  .app-container {
    .page-header {
      padding: 20px 24px;

      .title-section {
        .title-icon {
          width: 48px;
          height: 48px;
        }

        .title-text h1 {
          font-size: 28px;
        }
      }
    }

    .main-card {
      margin: -16px 24px 24px 24px;
    }

    .toolbar-section {
      padding: 20px 24px;
    }

    .project-list-container {
      .project-table {
        :deep(.el-table__body) tr td {
          padding: 16px 12px;
        }
      }
    }

    .project-preview .project-thumbnail {
      width: 100px;
      height: 56px;
    }

    .action-buttons {
      flex-direction: column;
      gap: 4px;

      .edit-btn, .export-btn, .delete-btn {
        font-size: 11px;
        padding: 4px 8px;
      }
    }

    .pagination-wrapper {
      padding: 16px 24px;
    }
  }
}

@media (max-width: 768px) {
  .app-container {
    .page-header {
      padding: 16px 20px;

      .title-section {
        flex-direction: column;
        gap: 12px;

        .title-text h1 {
          font-size: 24px;
        }

        .title-text p {
          font-size: 14px;
        }
      }

      .header-actions .create-btn {
        padding: 10px 20px;
        font-size: 14px;
      }
    }

    .main-card {
      margin: -12px 20px 20px 20px;
      border-radius: 16px;
    }

    .toolbar-section {
      padding: 16px 20px;

      .search-filters {
        .search-group {
          flex-direction: column;
          width: 100%;

          .search-input, .status-select {
            width: 100%;
            max-width: none;
          }
        }

        .action-buttons {
          width: 100%;
          justify-content: center;
        }
      }
    }

    .project-list-container {
      overflow-x: auto;

      .project-table {
        min-width: 800px;
      }
    }

    .pagination-wrapper {
      padding: 12px 20px;
    }

    .create-dialog {
      :deep(.el-dialog) {
        width: 95% !important;
        margin: 5vh auto;

        .el-dialog__header {
          padding: 20px 24px;
        }
      }

      .dialog-content {
        padding: 24px;
      }

      .dialog-actions {
        padding: 20px 24px;
        flex-direction: column;

        .cancel-btn, .submit-btn {
          width: 100%;
          margin: 0;
        }
      }
    }
  }
}

// 深色模式适配（可选）
@media (prefers-color-scheme: dark) {
  .app-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    .main-card {
      background: #1e1e1e;
      color: #ffffff;
    }

    .toolbar-section {
      background: #252525;
      border-bottom-color: #404040;
    }

    .pagination-wrapper {
      background: #252525;
      border-top-color: #404040;
    }
  }
}


</style>