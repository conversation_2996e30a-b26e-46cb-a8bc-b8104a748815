
:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  margin: 0;
  border-radius: 10px;

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: white;
  }
}

:deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  .el-dialog__close {
    color: white;
    font-size: 16px;
  }
}

:deep(.el-dialog__body) {
  height: 500px;
  padding: 16px;
  background: #fafbfc;
  overflow: hidden;
}

:deep(.el-dialog__footer) {
  background: linear-gradient(
    180deg,
    rgba(250, 251, 252, 0.8) 0%,
    #fafbfc 100%
  );
  padding: 0px 30px;
  margin: 0;
  // backdrop-filter: blur(10px);
}

.dialog-body {
  display: flex;
  gap: 24px;
  height: 100%;
}

.dialog-left {
  flex: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-box {
  width: 100%;
  max-width: 380px;
  min-height: 400px;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fd 100%);
  border: 2px dashed #e1e8ff;
  border-radius: 20px;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);

  &:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(102, 126, 234, 0.15);
  }

  :deep(.el-upload) {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 100%;
    background-color: transparent;
    border: none;
    border-radius: 20px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .el-upload__text {
      display: none;
    }

    &:hover {
      background-color: rgba(102, 126, 234, 0.05);
    }
  }

  :deep(.el-upload-list) {
    margin: 0;
    padding: 0;
    order: -1;
    flex-shrink: 0;
    width: 100%;
    box-sizing: border-box;

    &:not(:empty) + .el-upload .el-upload-dragger {
      display: none !important;
    }
  }

  &:has(.el-upload-list__item) {
    :deep(.el-upload-dragger) {
      display: none !important;
    }
  }

  :deep(.el-upload-list__item) {
    margin: 16px 16px 0 16px;
    width: calc(100% - 32px);
    box-sizing: border-box;
    background: linear-gradient(145deg, #ffffff 0%, #f8faff 100%);
    border-radius: 16px;
    border: 2px solid #e1e8ff;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.08);
    transition: all 0.3s ease;
    padding: 20px;
    position: relative;
    overflow: hidden;

    &:last-child {
      margin-bottom: 16px;
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    &:hover {
      box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
      transform: translateY(-2px);
      border-color: #667eea;
    }

    .el-upload-list__item-info {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .el-upload-list__item-name {
      color: #2d3748;
      font-weight: 600;
      font-size: 15px;
      margin: 0;
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;
      //   overflow: hidden;
      //   white-space: nowrap;
      //   text-overflow: ellipsis;
      max-width: 250px;

      // 修改文件名称
      .el-upload-list__item-file-name {
        // 默认为声音
        &::before {
          content: "🎵";
          font-size: 24px;
          display: inline-block;
          margin-right: 8px;
          flex-shrink: 0;
        }
        // 视频文件
        &[title*=".mp4"]::before,
        &[title*=".avi"]::before,
        &[title*=".mov"]::before,
        &[title*=".wmv"]::before,
        &[title*=".mkv"]::before,
        &[title*=".flv"]::before,
        &[title*=".rmvb"]::before,
        &[title*=".webm"]::before,
        &[title*=".mpeg"]::before,
        &[title*=".3gp"]::before {
          content: "🎬";
        }

      }

      .el-icon {
        display: none !important;
      }
    }

    .el-upload-list__item-status-label {
      display: none;
    }

    .el-upload-list__item-delete {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(255, 77, 79, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 77, 79, 0.2);
        transform: scale(1.1);
      }

      .el-icon {
        color: #ff4d4f;
        font-size: 14px;
      }
    }
  }

  .uploaded-file {
    text-align: center;
    padding: 24px;

    .file-preview {
      max-width: 100%;
      max-height: 200px;
      margin-bottom: 16px;
      border-radius: 12px;
    }

    .file-name {
      font-size: 16px;
      color: #2d3748;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .file-info {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 12px;

      .file-size,
      .file-type {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }

      .file-size {
        background: #f0f4ff;
        color: #667eea;
      }

      .file-type {
        background: #f0fff4;
        color: #38a169;
      }
    }
  }

  :deep(.el-upload-list__item.is-success) {
    animation: slideInUp 0.5s ease-out;
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.upload-empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px 20px;
}

.upload-icon {
  margin-bottom: 20px;
  text-align: center;

  &::before {
    content: "📁";
    font-size: 48px;
    display: block;
    margin-bottom: 8px;
    opacity: 0.7;
  }
}

.upload-desc {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4a5568;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 24px;
  text-align: center;

  .upload-link {
    color: #667eea;
    cursor: pointer;
    font-weight: 600;
    transition: color 0.3s ease;

    &:hover {
      color: #5a67d8;
    }
  }
}

.upload-tips {
  color: #718096;
  font-size: 13px;
  line-height: 1.6;
  margin: 0;
  padding: 0 24px;
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 8px;

  li {
    position: relative;
    padding-left: 16px;

    &::before {
      content: "•";
      color: #667eea;
      font-weight: bold;
      position: absolute;
      left: 0;
    }
  }
}

.dialog-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  padding: 20px 16px 16px 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  justify-content: flex-start;
  min-height: 0;
  max-height: 100%;
  overflow: hidden;
}

:deep(.el-form) {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
  }

  scrollbar-width: none;
  -ms-overflow-style: none;

  .el-row {
    margin-bottom: 0;
    flex-shrink: 0;

    &:not(:last-child) {
      margin-bottom: 12px;
    }
  }

  .el-form-item {
    margin-bottom: 3px;

    .el-form-item__label {
      color: #2d3748;
      font-size: 13px;
      font-weight: 600;
      margin-bottom: 3px;
      line-height: 1.2;
      display: block;
    }

    &:last-child {
      margin-top: 16px;
      margin-bottom: 0;
      padding-top: 8px;
      flex-shrink: 0;
    }
  }

  .el-radio-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;

    &:first-of-type {
      grid-template-columns: repeat(2, 1fr);

      .el-radio:nth-child(5) {
        grid-column: 1 / -1;
        justify-self: start;
      }
    }

    &:last-of-type {
      grid-template-columns: repeat(2, 1fr);
    }

    .el-radio {
      margin-right: 0;

      &.is-bordered {
        border-radius: 4px;
        border-color: #e2e8f0;
        padding: 3px 6px;
        font-size: 11px;
        text-align: center;
        min-height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.is-checked {
          border-color: #667eea;
          background-color: #667eea;
          color: white;

          .el-radio__label {
            color: white;
          }
        }
      }
    }
  }

  .el-select {
    width: 100% !important;

    .el-input__wrapper {
      border-radius: 4px;
      border-color: #e2e8f0;
      height: 24px;
      font-size: 12px;

      &.is-focus {
        border-color: #667eea;
      }
    }

    .el-input__inner {
      font-size: 12px;
    }
  }

  .phrase-select .el-input__wrapper {
    height: 32px;
  }

  .el-button {
    border-radius: 4px;
    padding: 5px 14px;
    font-weight: 500;
    font-size: 11px;
    flex: 1;
    height: 28px;

    &.el-button--primary {
      background: linear-gradient(135deg, #8b9cf7 0%, #9bb5ff 100%);
      border: none;
      color: white;

      &:disabled {
        background: #e2e8f0;
        color: #a0aec0;
        cursor: not-allowed;
      }
    }

    &.el-button--default {
      border-color: #e2e8f0;
      color: #4a5568;
    }
  }
}

:deep(.el-select-dropdown) {
  .phrase-option {
    padding: 0 !important;

    .option-content {
      padding: 8px 12px;

      .option-name {
        font-size: 13px;
        font-weight: 500;
        color: #2d3748;
        line-height: 1.2;
      }
    }

    &:hover .option-content .option-name {
      color: #667eea;
    }

    &.selected .option-content .option-name {
      color: #667eea;
      font-weight: 600;
    }
  }
}

/* 底部按钮区域样式 */
.dialog-footer {
  position: relative;

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 20px;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(102, 126, 234, 0.3),
        transparent
      );
      border-radius: 2px;
    }
  }

  .el-button {
    border-radius: 10px;
    padding: 14px 28px;
    font-weight: 600;
    font-size: 15px;
    height: 40px;
    min-width: 130px;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

    span {
      position: relative;
      z-index: 2;
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
      );
      transition: left 0.6s ease;
      z-index: 1;
    }

    &:hover::before {
      left: 100%;
    }

    &:hover {
      transform: translateY(-3px) scale(1.02);
    }

    &:active {
      transform: translateY(-1px) scale(0.98);
      transition: all 0.1s ease;
    }
  }

  .cancel-btn {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    box-shadow: 0 4px 20px rgba(108, 117, 125, 0.15);
    border: 2px solid rgba(233, 236, 239, 0.8);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

    &:hover {
      background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
      color: #495057;
      box-shadow: 0 8px 32px rgba(108, 117, 125, 0.25);
      border-color: rgba(222, 226, 230, 0.9);
    }

    &:active {
      box-shadow: 0 2px 12px rgba(108, 117, 125, 0.2);
    }
  }

  .submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 6px 28px rgba(102, 126, 234, 0.35);
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.2);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.1) 100%
      );
      border-radius: 16px;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .submit-icon {
      margin-right: 10px;
      font-size: 18px;
      animation: pulse 2.5s infinite;
      filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.3));
    }

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      box-shadow: 0 10px 40px rgba(102, 126, 234, 0.5);
      border-color: rgba(255, 255, 255, 0.3);

      &::after {
        opacity: 1;
      }
    }

    &:active {
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    }

    &:disabled {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      color: #64748b;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 2px 12px rgba(100, 116, 139, 0.1);
      border: 2px solid #e2e8f0;
      opacity: 1;
      position: relative;

      &::before {
        display: none;
      }

      .submit-icon {
        animation: none;
        color: #94a3b8;
        opacity: 0.8;
        filter: none;
      }

      span {
        color: #64748b;
        font-weight: 500;
        position: relative;
        z-index: 2;
      }

      &::after {
        display: none;
      }

      &:hover {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        transform: none;
        box-shadow: 0 2px 12px rgba(100, 116, 139, 0.1);
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}
