import request from '@/utils/request'


// 查询视频合成
export function listMusetalk(query) {
  return request({
    url: '/platform/video/list',
    method: 'get',
    params: query
  })
}
//创建视频合成 M版
export function createMusetalk(data) {
  return request({
    url: '/platform/video/add',
    method: 'post',
    data: data
  })
}
//根据任务结果动态调整任务状态  M版
export function genStatus(id) {
  return request({
    url: `/platform/video/genStatus/${id}`,
    method: 'get',
  })
}

// 删除合成任务
export function delMusetalk(taskIds) {
  return request({
    url: '/platform/video/' + taskIds,
    method: 'delete'
  })
}



// 创建视频合成任务 V版
export function createVideoSynthesis(data) {
  return request({
    url: '/platform/video/synthesis',
    method: 'post',
    data: data
  })
}

/**
 * 上传媒体文件(视频或音频)  V版
 */
export function uploadMedia(file, type) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: `/platform/video/upload/${type}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 600000
  })
}

// 获取可用的视频合成模型列表 V版
export function getAvailableModels() {
  return request({
    url: '/platform/video/models',
    method: 'get'
  })
}

// 查询视频合成任务状态 V版
export function getTaskStatus(taskNo) {
  return request({
    url: '/platform/video/statusTaskNo/'+taskNo,
    method: 'get'
  })
}

// 创建视频合成任务 H版
export function createHeygem(data) {
  return request({
    url: '/platform/video/createHeygem',
    method: 'post',
    data: data
  })
}