<template>
  <el-drawer v-model="drawerVisible" title="添加素材" direction="rtl" size="40%"
    style="background-color:rgba(44, 44, 44, 0.8) ;" class="material-selector-drawer" :before-close="handleClose">
    <template #header>
      <div class="drawer-header">
        <h3>{{ headerTitle }}</h3>
        <div class="header-actions">
          <el-button type="primary" :icon="Upload" @click="handleUpload">
            上传{{ mediaTypeLabel }}
          </el-button>
        </div>
      </div>
    </template>

    <div class="material-content">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input v-model="searchKeyword" placeholder="搜索素材名称..." :prefix-icon="Search" clearable
          @change="handleSearch" />
        <el-select v-model="sortBy" placeholder="排序方式" style="width: 120px" @change="handleSortChange">
          <el-option label="创建时间" value="utcCreate:Desc" />
          <el-option label="文件名" value="title:Desc" />
          <el-option label="修改时间" value="utcModified:Desc" />
        </el-select>
        <span class="go-media-lib-link" @click="handleGoToMediaLib">
          去媒资库上传
        </span>
      </div>

      <!-- 媒体类型标签 -->
      <div class="media-type-tabs">
        <div v-for="tab in mediaTypeTabs" :key="tab.value" class="media-tab"
          :class="{ active: currentMediaTypeFilter === tab.value }" @click="handleMediaTypeChange(tab.value)">
          {{ tab.label }}
        </div>
      </div>

      <!-- 素材网格 -->
      <div v-loading="loading" class="material-grid">
        <div v-for="material in materialList" :key="material.mediaId" class="material-item"
          :class="{ selected: selectedMaterials.includes(material.mediaId) }" @click="toggleSelect(material)">
          <div class="material-preview">
            <img v-if="material.coverUrl" :src="material.coverUrl" :alt="material.title" @error="handleImageError" />
            <div v-else class="no-preview">
              <el-icon>
                <Picture />
              </el-icon>
            </div>

            <!-- 视频时长 -->
            <div v-if="material.mediaType === 'video' && material.duration" class="duration">
              {{ formatDuration(material.duration) }}
            </div>

            <!-- 选中标识 -->
            <div v-if="selectedMaterials.includes(material.mediaId)" class="selected-overlay">
              <el-icon class="check-icon">
                <Check />
              </el-icon>
            </div>
          </div>

          <div class="material-info">
            <div class="material-title" :title="material.title">
              {{ material.title }}
            </div>
            <div class="material-meta">
              <span v-if="material.size">{{ formatFileSize(material.size) }}</span>
              <span v-if="material.createTime">{{ formatTime(material.createTime) }}</span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && materialList.length === 0" class="empty-state">
          <el-empty description="暂无素材" />
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
          :page-sizes="[20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handlePageChange" />
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <span class="selected-count">
          已选择 {{ selectedMaterials.length }} 个素材
        </span>
        <div>
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" :disabled="selectedMaterials.length === 0" @click="handleConfirm">
            确定添加
          </el-button>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Search, Picture, Check } from '@element-plus/icons-vue'
import { iceNPMManager } from '../utils/iceNPMManager'

interface Material {
  mediaId: string
  title: string
  coverUrl?: string
  duration?: number
  size?: number
  createTime: string
  mediaType: 'video' | 'audio' | 'image'
  businessType?: string
  status?: string
}

interface Props {
  visible: boolean
  mediaType: 'video' | 'audio' | 'image'
  mode: 'project' | 'template'
  editorId: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'select', materials: Material[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 抽屉状态
const drawerVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 数据状态
const loading = ref(false)
const materialList = ref<Material[]>([])
const selectedMaterials = ref<string[]>([])
const currentCategory = ref('')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const scrollToken = ref('') // 对应ScrollToken
const currentMediaTypeFilter = ref('')
const entityId = ref('') // 实体ID，预留参数
const searchLibName = ref('') // 搜索库名称，预留参数
const sortBy = ref('utcCreate:Desc') // 排序方式

// 媒体类型标签
const mediaTypeTabs = ref([
  { label: '所有', value: '' },
  { label: '视频', value: 'video' },
  { label: '图片', value: 'image' },
  { label: '音频', value: 'audio' }
  // 注意：根据文档说明，图片与音视频无法同时搜索，text类型可能不被支持
])

// 计算属性
const headerTitle = computed(() => {
  const typeMap: Record<string, string> = {
    video: '添加视频素材',
    audio: '添加音频素材',
    image: '添加图片素材'
  }
  return typeMap[props.mediaType] || '添加素材'
})

const mediaTypeLabel = computed(() => {
  const labelMap: Record<string, string> = {
    video: '视频',
    audio: '音频',
    image: '图片'
  }
  return labelMap[props.mediaType] || '素材'
})

// 监听抽屉打开
watch(drawerVisible, (visible) => {
  if (visible) {
    resetData()
    loadMaterials()
  }
})

// 设置搜索配置 (预留方法，方便后续配置EntityId和SearchLibName)
const setSearchConfig = (config: {
  entityId?: string
  searchLibName?: string
}) => {
  if (config.entityId !== undefined) {
    entityId.value = config.entityId
  }
  if (config.searchLibName !== undefined) {
    searchLibName.value = config.searchLibName
  }
}

// 重置数据
const resetData = () => {
  selectedMaterials.value = []
  materialList.value = []
  currentPage.value = 1
  searchKeyword.value = ''
  currentCategory.value = ''
  currentMediaTypeFilter.value = ''
  total.value = 0
  scrollToken.value = '' // 对应ScrollToken
  // 注意：entityId 和 searchLibName 通常不需要重置，它们是配置参数
}

// ICE API调用
const callICEApi = async (param: any): Promise<any> => {
  if (!iceNPMManager.isReady()) {
    throw new Error('ICE客户端未初始化')
  }

  try {
    const response = await iceNPMManager.SearchMedia(param)
    return response
  } catch (error) {
    console.error('ICE API调用失败:', error)
    throw error
  }
}

// 加载素材列表
const loadMaterials = async () => {
  loading.value = true
  try {
    // 构建过滤条件 (符合媒资搜索协议)
    const matchConditions: string[] = []

    // 媒体类型过滤 (精确匹配)
    const mediaType = currentMediaTypeFilter.value || props.mediaType
    if (mediaType && mediaType !== '') {
      matchConditions.push(`mediaType == '${mediaType}'`)
    }

    // 搜索关键词过滤 (模糊匹配，使用title字段)
    if (searchKeyword.value) {
      matchConditions.push(`title = '${searchKeyword.value}'`)
    }

    // 构建请求参数 (完整按照SearchMedia接口规范)
    const params: any = {
      PageSize: pageSize.value, // 每页返回的数据条数，默认10，最大50
      PageNo: currentPage.value, // 当前页码，默认1
      SortBy: sortBy.value // 排序字段和排序顺序
    }

    // 添加过滤条件 (媒资搜索协议语法)
    if (matchConditions.length > 0) {
      params.Match = matchConditions.join(' and ')
    }

    // 添加实体ID (如果配置了)
    if (entityId.value) {
      params.EntityId = entityId.value
    }

    // 添加搜索库名称 (如果配置了)
    if (searchLibName.value) {
      params.SearchLibName = searchLibName.value
    }

    // 添加翻页标识 (用于优化分页性能，特别是PageNo > 200时)
    if (scrollToken.value) {
      params.ScrollToken = scrollToken.value
    }

    const response = await callICEApi(params)

    if (response) {
      const { MediaInfoList = [], ScrollToken: newScrollToken, Total = 0 } = response

      // 转换API数据到组件数据格式
      const materials: Material[] = MediaInfoList.map((item: any) => ({
        mediaId: item.MediaId,
        title: item.MediaBasicInfo?.Title || item.FileInfoList?.[0]?.FileBasicInfo?.FileName || '未知素材',
        coverUrl: item.MediaBasicInfo?.CoverURL || item.MediaBasicInfo?.Snapshots?.[0],
        duration: item.FileInfoList?.[0]?.FileBasicInfo?.Duration ? parseFloat(item.FileInfoList[0].FileBasicInfo.Duration) : undefined,
        size: item.FileInfoList?.[0]?.FileBasicInfo?.FileSize ? parseInt(item.FileInfoList[0].FileBasicInfo.FileSize) : undefined,
        createTime: item.MediaBasicInfo?.CreateTime || item.FileInfoList?.[0]?.FileBasicInfo?.CreateTime,
        mediaType: item.MediaBasicInfo?.MediaType || item.FileInfoList?.[0]?.FileBasicInfo?.FileMediaType,
        businessType: item.MediaBasicInfo?.BusinessType,
        status: item.MediaBasicInfo?.Status
      }))

      if (currentPage.value === 1) {
        materialList.value = materials
      } else {
        materialList.value = [...materialList.value, ...materials]
      }

      scrollToken.value = newScrollToken || ''
      total.value = Total
    }
  } catch (error) {
    console.error('加载素材失败:', error)
    ElMessage.error('加载素材失败')
  } finally {
    loading.value = false
  }
}

// 切换选择
const toggleSelect = (material: Material) => {
  const index = selectedMaterials.value.indexOf(material.mediaId)
  if (index > -1) {
    selectedMaterials.value.splice(index, 1)
  } else {
    selectedMaterials.value.push(material.mediaId)
  }
}

// 处理上传
const handleUpload = () => {
  ElMessage.info('上传功能待实现')
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  scrollToken.value = '' // 重置ScrollToken
  loadMaterials()
}

// 处理排序变化
const handleSortChange = () => {
  currentPage.value = 1
  scrollToken.value = '' // 重置ScrollToken
  loadMaterials()
}

// 处理跳转到媒资库
const handleGoToMediaLib = () => {
  ElMessage.info('跳转到媒资库功能待实现')
  // 这里可以实现跳转到媒资库的逻辑
}

// 处理媒体类型变化
const handleMediaTypeChange = (mediaType: string) => {
  currentMediaTypeFilter.value = mediaType
  currentPage.value = 1
  scrollToken.value = '' // 重置ScrollToken
  loadMaterials()
}

// 处理分页
const handlePageChange = () => {
  loadMaterials()
}

const handleSizeChange = () => {
  currentPage.value = 1
  scrollToken.value = '' // 重置ScrollToken
  loadMaterials()
}

// 处理图片错误
const handleImageError = (e: Event) => {
  const target = e.target as HTMLImageElement
  target.style.display = 'none'
}

// 格式化时长
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleDateString()
}

// 处理关闭
const handleClose = () => {
  drawerVisible.value = false
}

// 处理确认
const handleConfirm = () => {
  const selectedMaterialData = materialList.value.filter(
    material => selectedMaterials.value.includes(material.mediaId)
  )
  emit('select', selectedMaterialData)
  handleClose()
}

// 暴露方法给父组件
defineExpose({
  setSearchConfig
})
</script>

<style lang="scss" scoped>
.material-selector-drawer {
  :deep(.el-drawer) {
    background: #2c2c2c;
    border: none;
    box-shadow: -2px 0 12px rgba(0, 0, 0, 0.4);
  }

  :deep(.el-drawer__body) {
    padding: 0;
    display: flex;
    background: #2c2c2c;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    background: #2c2c2c;
    border-bottom: 1px solid #3a3a3a;
  }

  :deep(.el-drawer__title) {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
  }

  :deep(.el-drawer__close-btn) {
    color: #cccccc;

    &:hover {
      color: #ffffff;
    }
  }
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
  }
}

.material-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #2c2c2c;
}

.search-bar {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  align-items: center;

  .el-input {
    flex: 1;

    :deep(.el-input__wrapper) {
      background-color: #3a3a3a;
      border: 1px solid #4a4a4a;
      box-shadow: none;
      border-radius: 6px;
    }

    :deep(.el-input__inner) {
      color: #ffffff;
      font-size: 13px;

      &::placeholder {
        color: #999999;
      }
    }

    :deep(.el-input__prefix-inner) {
      color: #999999;
    }
  }

  .el-select {
    width: 120px;

    :deep(.el-input__wrapper) {
      background-color: #3a3a3a;
      border: 1px solid #4a4a4a;
      box-shadow: none;
      border-radius: 6px;
    }

    :deep(.el-input__inner) {
      color: #ffffff;
      font-size: 12px;
    }

    :deep(.el-input__suffix-inner) {
      color: #999999;
    }
  }

  .el-button {
    font-size: 12px;
    height: 32px;
    padding: 6px 12px;
    border-radius: 6px;
    white-space: nowrap;

    &.el-button--primary {
      background-color: #409eff;
      border-color: #409eff;

      &:hover {
        background-color: #337ecc;
        border-color: #337ecc;
      }
    }
  }
}

.media-type-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding: 0 2px;
}

.media-tab {
  flex: 1;
  padding: 8px 12px;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #cccccc;
  background-color: #3a3a3a;
  border: 1px solid #4a4a4a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #4a4a4a;
    border-color: #5a5a5a;
    color: #ffffff;
  }

  &.active {
    background-color: #409eff;
    border-color: #409eff;
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
  }
}

.material-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
  overflow-y: auto;
  padding: 0 4px 16px 0;

  // Loading状态样式
  :deep(.el-loading-mask) {
    background-color: rgba(44, 44, 44, 0.8);

    .el-loading-spinner {
      .el-loading-text {
        color: #ffffff;
      }

      .circular {
        .path {
          stroke: #409eff;
        }
      }
    }
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
    }
  }
}

.material-item {
  border: 1px solid #3a3a3a;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #3a3a3a;
  position: relative;

  &:hover {
    border-color: #4a4a4a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(-1px);
  }

  &.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.3);
  }
}

.material-preview {
  position: relative;
  width: 100%;
  height: 68px;
  background: #2a2a2a;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .no-preview {
    color: #666666;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .duration {
    position: absolute;
    bottom: 3px;
    right: 3px;
    background: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 1px 3px;
    border-radius: 2px;
    font-size: 9px;
    font-weight: 500;
    line-height: 1.2;
  }

  .selected-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(64, 158, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;

    .check-icon {
      color: #409eff;
      font-size: 14px;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 50%;
      padding: 2px;
    }
  }
}

.material-info {
  padding: 6px 8px;
  background: #3a3a3a;
}

.material-title {
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 11px;
  line-height: 1.3;
}

.material-meta {
  font-size: 9px;
  color: #999999;
  display: flex;
  justify-content: space-between;
  line-height: 1.2;

  span {
    &:first-child {
      color: #cccccc;
    }
  }
}

.empty-state {
  grid-column: 1 / -1;
  padding: 60px 20px;
  text-align: center;

  :deep(.el-empty) {
    .el-empty__image {
      width: 80px;
      height: 80px;

      svg {
        fill: #666666;
      }
    }

    .el-empty__description {
      color: #999999;
      font-size: 14px;
    }
  }
}

.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  background: #2c2c2c;
  padding: 12px 0;
  border-top: 1px solid #3a3a3a;

  :deep(.el-pagination) {

    .el-pagination__total,
    .el-pagination__jump {
      color: #cccccc;
      font-size: 12px;
    }

    .btn-prev,
    .btn-next,
    .el-pager li {
      font-size: 12px;
      min-width: 28px;
      height: 28px;
      line-height: 26px;
      background-color: #3a3a3a;
      color: #ffffff;
      border: 1px solid #4a4a4a;
      border-radius: 4px;
      margin: 0 2px;

      &:hover {
        background-color: #4a4a4a;
        border-color: #5a5a5a;
      }

      &.is-active {
        background-color: #409eff;
        border-color: #409eff;
        color: #ffffff;
      }
    }

    .el-pagination__sizes {
      .el-select {
        .el-input {
          :deep(.el-input__wrapper) {
            background-color: #3a3a3a;
            border: 1px solid #4a4a4a;
          }
        }
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #3a3a3a;
  background: #2c2c2c;

  .selected-count {
    color: #cccccc;
    font-size: 13px;
  }

  .el-button {
    padding: 6px 16px;
    font-size: 13px;
    border-radius: 4px;

    &:not(.el-button--primary) {
      background-color: #3a3a3a;
      border-color: #4a4a4a;
      color: #ffffff;

      &:hover {
        background-color: #4a4a4a;
        border-color: #5a5a5a;
      }
    }

    &.el-button--primary {
      background-color: #409eff;
      border-color: #409eff;

      &:hover {
        background-color: #337ecc;
        border-color: #337ecc;
      }

      &:disabled {
        background-color: #53a8ff;
        border-color: #53a8ff;
        opacity: 0.5;
      }
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .material-grid {
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
  }
}

@media (max-width: 768px) {
  .material-grid {
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 6px;
  }

  .material-preview {
    height: 60px;
  }

  .material-info {
    padding: 4px 6px;
  }

  .material-title {
    font-size: 10px;
  }

  .material-meta {
    font-size: 8px;
  }
}
</style>