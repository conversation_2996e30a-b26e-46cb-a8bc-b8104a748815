// src/views/tool/MatrixMix/utils/fileUtils.ts
// 智能识别文件类型
import { inferMediaType } from './commonUtils';

export const detectFileCategory = (file: File, activeTab: string): string => {
    return inferMediaType(file.name, activeTab);
}

/**
 * 根据文件url下载文件
 * @param url 文件下载地址
 * @param filename 可选，下载保存的文件名
 */
export function downloadFileByUrl(url: string, filename?: string) {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    if (filename) {
        a.download = filename;
    } else {
        // 异常报错
        throw new Error('无法下载没有文件名的文件');
    }
    a.target = '_blank';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}