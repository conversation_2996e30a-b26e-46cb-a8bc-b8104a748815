/**
 * 阿里云ICE剪辑工程相关API
 * 通过后端代理调用阿里云ICE SDK
 */
import request from '@/utils/request';

// ============================================================================
// 类型定义 - 根据阿里云ICE官方文档
// ============================================================================

export interface CreateProjectRequest {
  title: string;
  timeline?: string;
  description?: string;
  templateId?: string;
  clipsParam?: string;
  coverURL?: string;
  businessConfig?: any;
}

export interface UpdateProjectRequest {
  projectId: string;
  timeline: string;
  title?: string;
}

export interface ProjectInfo {
  ProjectId: string;
  Title: string;
  Description?: string;
  Timeline: any; // Timeline对象，不是字符串
  TemplateId?: string;
  ClipsParam?: string;
  CoverURL?: string;
  Status: number;
  StatusName?: string;
  CreateTime?: string;
  ModifiedTime?: string;
  Duration?: number;
  CreateSource?: string;
  ModifiedSource?: string;
  TemplateType?: string;
  BusinessConfig?: any;
  ProjectType?: string;
  BusinessStatus?: string;
}

export interface ProjectListParams {
  keyword?: string;
  status?: string;
  nextToken?: string;
  maxResults?: number;
}

export interface ProjectListResponse {
  ProjectList: ProjectInfo[];
  NextToken?: string;
  MaxResults?: number;
  RequestId?: string;
}

// 导出相关接口定义
export interface SubmitProjectExportRequest {
  projectId?: string;
  timeline?: string;
  exportType?: string;
  bucket: string;
  prefix?: string;
  width?: number;
  height?: number;
  notifyAddress?: string;
}

export interface SubmitProjectExportResponse {
  RequestId: string;
  JobId: string;
}

export interface ProjectExportJob {
  JobId: string;
  ProjectId: string;
  ExportType: string;
  Status: string;
  Code?: string;
  Message?: string;
  ExportResult?: {
    Timeline?: string;
    ProjectUrl?: string;
  };
  UserData?: string;
}

export interface GetProjectExportJobResponse {
  RequestId: string;
  ProjectExportJob: ProjectExportJob;
}

export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// ============================================================================
// API接口
// ============================================================================

/**
 * 创建剪辑工程
 */
export function createEditingProject(data: CreateProjectRequest): Promise<ApiResponse<ProjectInfo>> {
  return request({
    url: '/video/project/create',
    method: 'post',
    data
  });
}

/**
 * 获取剪辑工程详情
 */
export function getEditingProject(projectId: string): Promise<ApiResponse<ProjectInfo>> {
  return request({
    url: `/video/project/${projectId}`,
    method: 'get'
  });
}

/**
 * 更新剪辑工程
 */
export function updateEditingProject(data: UpdateProjectRequest): Promise<ApiResponse<any>> {
  const { projectId, ...updateData } = data;
  return request({
    url: `/api/ice/projects/${projectId}`,
    method: 'put',
    data: updateData
  });
}

/**
 * 删除剪辑工程
 */
export function deleteEditingProjects(projectIds: string[]): Promise<ApiResponse<any>> {
  return request({
    url: '/video/project/delete',
    method: 'post',
    data: { 
      projectIds: projectIds.join(',') 
    }
  });
}

/**
 * 获取剪辑工程列表
 */
export function listEditingProjects(params: ProjectListParams = {}): Promise<ApiResponse<ProjectListResponse>> {
  return request({
    url: '/video/project/list',
    method: 'get',
    params: {
      keyword: params.keyword,
      status: params.status,
      nextToken: params.nextToken,
      maxResults: params.maxResults || 20
    }
  });
}

/**
 * 提交云剪辑工程导出任务
 */
export function submitProjectExportJob(data: SubmitProjectExportRequest): Promise<ApiResponse<SubmitProjectExportResponse>> {
  return request({
    url: '/video/project/export',
    method: 'post',
    data
  });
}

/**
 * 查询云剪辑工程导出任务状态和结果
 */
export function getProjectExportJob(jobId: string): Promise<ApiResponse<GetProjectExportJobResponse>> {
  return request({
    url: `/video/project/export/${jobId}`,
    method: 'get'
  });
}

/**
 * 获取剪辑工程关联的素材
 */
export function getProjectMaterials(projectId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/api/ice/projects/${projectId}/materials`,
    method: 'get'
  });
}

/**
 * 添加素材到剪辑工程
 */
export function addMaterialsToProject(projectId: string, mediaIds: string[]): Promise<ApiResponse<any>> {
  return request({
    url: `/api/ice/projects/${projectId}/materials`,
    method: 'post',
    data: { mediaIds }
  });
}

/**
 * 从剪辑工程移除素材
 */
export function removeMaterialsFromProject(projectId: string, materialIds: string[]): Promise<ApiResponse<any>> {
  return request({
    url: `/api/ice/projects/${projectId}/materials`,
    method: 'delete',
    data: { materialIds }
  });
} 